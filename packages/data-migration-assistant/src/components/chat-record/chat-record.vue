<script lang="ts" setup>
import { useFormat } from '@/composables/date';
import { computed, nextTick, onMounted, onUnmounted, onUpdated, reactive, ref, watch } from 'vue';
import _ from 'lodash';
import Conversation from './conversation.vue';

const props = defineProps({
    dataProvider: {
        type: Function,
        default: () => () => [],
    },
    finishedText: {
        type: String,
        default: '没有更多了',
    },
    type: {
        type: String,
        default: 'scroll', // scroll | pagination | reverse
    },
    pageSize: {
        type: Number,
        default: 10,
    },
    // 是否分页滚动加载
    isPaginate: {
        type: Boolean,
        default: true,
    },
    // 滚动条与底部距离小于 offset 时触发 load 事件
    reachBottomOffset: {
        type: Number,
        default: 100,
    },
    // 是否在初始化时立即执行滚动位置检查
    immediateCheck: {
        type: Boolean,
        default: true,
    },
    // 节流时延，单位为ms
    infiniteScrollDelay: {
        type: Number,
        default: 200,
    },
    height: {
        type: String,
        default: '50vh',
    },
});
const isPagination = computed(() => props.type === 'pagination');
const isScroll = computed(() => props.type === 'scroll');
const isReverseScroll = computed(() => props.type === 'reverse');
const timelineRef = ref<any>(null);
const scrollHeight = ref(0);
watch(
    () => props.type,
    async (val) => {
        if (val !== 'scroll') {
            await nextTick();
            await loadMore();
        }
    },
    { immediate: true },
);
// 是否完成加载所有数据
const isFinished = ref(false);
// 是否正在加载数据
const isLoading = ref(false);
// 是否底部加载
const timelineLoading = ref(false);
// 是否禁止加载
const isDisabled = ref(false);

const timelineData = ref<any[]>([]);

// 分页参数
const pageParams = reactive({
    keyword: '',
    offset: 0,
    limit: props.pageSize,
    total: 0,
});

const resetPageQuery = async () => {
    pageParams.offset = 0;
    pageParams.total = 0;
    isFinished.value = false;
    isDisabled.value = false;
    timelineData.value = [];
    await loadMore();
};
defineExpose({ resetPageQuery });
async function loadMore() {
    if (isFinished.value || isDisabled.value) {
        return;
    }
    // 将 loading 设置为 true，表示列表处于加载状态
    isLoading.value = true;
    const res: any = await props.dataProvider(pageParams);
    const { rows, offset, limit, total } = res;

    // 更新列表
    timelineData.value = props.isPaginate && !isPagination.value ? rows.concat(timelineData.value) : rows;

    if (props.isPaginate) {
        // 更新分页参数
        pageParams.offset += pageParams.limit;
        !isPagination.value ? (pageParams.total += total) : (pageParams.total = total);
        // 停止加载
        isLoading.value = false;
        // 标记加载完成
        if (timelineData.value.length >= total) {
            isFinished.value = true;
        }
    } else {
        isLoading.value = false;
        isFinished.value = true;
    }
    if (!props.isPaginate) {
        isDisabled.value = true;
    }
    await nextTick();
    if (isReverseScroll.value) {
        // 滚动到底部
        const el = timelineRef.value.$el;
        if (el) {
            el.scrollTop = el.scrollHeight - scrollHeight.value;
            scrollHeight.value = el.scrollHeight;
        }
    }
}

/**
 * 获取会话主体类型
 * @param timeline 会话数据
 */
const getUserName = (timeline: any) => {
    let userName = '';
    switch (timeline.origin) {
        // 群聊客户发送的消息
        case 0:
            userName = timeline.externalUserName || '客户消息';
            break;
        // 群聊工作人员发送的消息
        case 1:
            userName = timeline.servicerName || '客户消息';
            break;
        // 微信客户发送的消息
        case 3:
            userName = timeline.clientName || '客户消息';
            break;
        // 系统推送的事件消息
        case 4:
            userName = '系统消息';
            break;
        // 接待人员在企业微信客户端发送的消息
        case 5:
            userName = timeline.servicerName || '客服消息';
            break;
        default:
            userName = timeline.senderName || '客服消息';
    }
    return userName;
};
const emit = defineEmits(['dialogCancel']);
const preventEscCloseEvent = async (event: any) => {
    // 检查是否按下了ESC键
    if (event.key === 'Escape') {
        event.preventDefault(); // 阻止默认的事件行为，防止关闭整个弹窗
        const previewImage = document.querySelector('.el-image-viewer__mask');
        if (previewImage) { // 检查图片元素是否存在
            // @ts-ignore
            previewImage.click(); // 点击蒙层关闭图片预览
        } else {
            emit('dialogCancel');
        }
    }
};
const _debouncePreventEsc = _.debounce(preventEscCloseEvent, 200);

onMounted(() => {
    listenEscEvent();
});
const listenEscEvent = (async () => {
    await nextTick();
    // 监听keydown事件
    document.addEventListener('keydown', _debouncePreventEsc);
});
onUnmounted(() => {
    // 移除keydown事件
    document.removeEventListener('keydown', _debouncePreventEsc);
});
const onChangePage = (pageIndex: number) => {
    pageParams.offset = (pageIndex - 1) * pageParams.limit;
    loadMore();
};
</script>
<template>
    <div class="chat-record-wrapper">
        <div :style="{height: height }">
            <el-timeline
                ref="timelineRef"
                v-loading="isLoading"
                v-infinite-scroll="loadMore"
                :infinite-scroll-distance="reachBottomOffset"
                :infinite-scroll-disabled="isLoading || !isScroll"
                :infinite-scroll-delay="infiniteScrollDelay"
                :infinite-scroll-immediate="!isPagination && immediateCheck"
                class="timeline-wrapper"
            >
                <p v-if="isReverseScroll && !isFinished" class="reverse-load-icon" @click="loadMore"><el-icon><ArrowUpBold /></el-icon></p>
                <el-timeline-item
                    v-for="timeline in timelineData"
                    :key="timeline.id"
                    :timestamp="`${getUserName(timeline)} ${useFormat(timeline.sendTime || timeline.msgTime,'YYYY-MM-DD HH:mm:ss')}`"
                    placement="top"
                >
                    <el-card>
                        <div class="conversation-line">
                            <conversation :timeline="timeline"></conversation>
                        </div>
                    </el-card>
                </el-timeline-item>
                <p v-if="timelineLoading" class="timeline-loading">Loading...</p>
                <el-empty v-if="!timelineData || !timelineData.length" class="chat-record-empty" description="暂无相关会话记录" />
                <p v-else-if="isFinished" class="timeline-loading">{{ finishedText }}</p>
            </el-timeline>
        </div>
        <footer v-if="isPagination">
            <el-pagination
                background
                layout="prev, pager, next"
                :total="pageParams.total"
                @current-change="onChangePage"
            >
            </el-pagination>
        </footer>
    </div>
</template>
<style lang="scss">
.chat-record-wrapper {
    .timeline-wrapper {
        height: 100%;
        overflow-y: auto;
        overflow-anchor: none;
        position: relative;

        .timeline-loading {
            text-align: center;
        }

        .reverse-load-icon {
            text-align: center;
            padding: 12px;
            cursor: pointer;
        }
    }

    .chat-record-empty {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
    }
}
</style>
