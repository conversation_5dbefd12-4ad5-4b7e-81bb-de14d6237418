<!--KV 形式的卡片承载-->
<script setup lang="ts">
import { computed } from 'vue';
import CurlyBrace from '@/components/curly-brace.vue';

const props = defineProps({
    info: {
        type: Object,
        required: true,
    },
    // 字段定义
    columns: {
        type: Array,
        default: () => [],
    },
});

const labelMap = computed<Record<string, any>>(() => {
    let map = {};
    (props.columns || []).reduce((acc: any, col: any) => {
        acc[col.prop] = col.name || col.prop;
        return acc;
    }, map);
    return map;
});

const kvList = computed<Array<any>>(() => Object.keys(props.info).map((k: string) => ({
    label: labelMap.value[k] || k,
    value: props.info[k],
})));

</script>

<template>
    <div class="kv-card-wrapper">
        <el-card shadow="never" border-style="none">
            <el-row v-for="kv in kvList" class="kv-row" align="bottom">
                <el-col :span="8" :style="{alignSelf: Array.isArray(kv.value) ? 'center' : ''}">{{ kv.label }}</el-col>
                <el-col :span="16">
                    <div v-if="Array.isArray(kv.value)" class="array-wrapper">
                        <curly-brace class="curly-brace-list"></curly-brace>
                        <template v-for="child in kv.value">
                            <kv-card :info="child"></kv-card>
                        </template>
                    </div>
                    <template v-else>
                        <span>{{ kv.value }}</span>
                    </template>
                </el-col>
            </el-row>
        </el-card>
    </div>
</template>

<style lang="scss">
.kv-card-wrapper {
    user-select: text;

    .kv-row {
        min-height: 32px;
        border-bottom: 1px dotted var(--oa-border-color);
        padding-bottom: 2px;

        .array-wrapper {
            position: relative;
        }

        .curly-brace-list {
            height: 100%;
            position: absolute;
            left: -20px;
        }
    }
}
</style>
