import Layout from '@/layout/index.vue';
import NotFound from '@/layout/not-found.vue';
import homeRoutes from '@/router/dynamic/home';

import { RouteRecordRaw } from 'vue-router';

const moduleRoute = import.meta.glob('../views/**/route.ts', { eager: true });

let moduleRoutes: RouteRecordRaw[] = [];
for (let mod in moduleRoute) {
    console.log('moduleRoute', moduleRoute[mod].default);
    moduleRoutes = moduleRoutes.concat(...moduleRoute[mod].default);
}
export const dynamicRoutes = [
    {
        path: '/',
        name: 'layout',
        component: Layout,
        meta: {
            name: '首页',
        },
        redirect: {
            name: homeRoutes[0].name,
        },
        children: [
            ...homeRoutes,
            ...moduleRoutes,
            {
                path: '/:pathMatch(.*)',
                name: 'notfound',
                component: NotFound,
                meta: {
                    hidden: true,
                },
            },
        ],
    },
];
