/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import dayjs from 'dayjs';
import StatisticsApi from '@/api/statistics-api';
import * as utils from '@/common/utils';
import { computed, reactive } from 'vue';
import createLoadingModel from '@/model/loading';

export const createDetailDataController = () => {
    const loadingModelTable = createLoadingModel();

    // 工具参数
    const toolsParams = reactive({
        regionId: '1', // 区域
        uploadStatus: '', // 上传状态
        taskId: '', // 任务ID
        startTime: '', // 开始时间
        endTime: '', // 结束时间
        pageNum: 1, // 页码
        pageSize: 10, // 每页条数
    });

    // 是否可以查询数据
    const isEnableFetchData = computed(() => {
        if (
            !toolsParams.regionId
            || !toolsParams.taskId
            || !toolsParams.startTime
            || !toolsParams.endTime
        ) {
            return false;
        }
        return true;
    });

    // 结果数据
    const resultData = reactive({
        list: [], // 数据列表
        total: 0, // 总条数
    });

    // 区域选项
    const regionOptions = computed(() => [
        {
            label: 'region1',
            value: '1',
        },
        {
            label: 'region2',
            value: '2',
        },
    ]);

    // 状态选项
    const uploadStatusOptions = computed(() => [
        {
            label: '上报成功',
            value: 1,
        },
        {
            label: '上报失败',
            value: 2,
        },
        {
            label: '待上报',
            value: 0,
        },
        {
            label: '上报中',
            value: 3,
        },
        {
            label: '上报之后数据有修改',
            value: 4,
        },
        {
            label: '无需上报',
            value: 5,
        },
    ]);

    /**
     * 初始化工具参数
     * <AUTHOR>
     * @date 2025-11-06
     * @param {Object} info
     */
    const initToolsParams = (info: any) => {
        toolsParams.uploadStatus = info.uploadStatus;
        toolsParams.taskId = info.taskId || '';
        toolsParams.startTime = info.startTime || '';
        toolsParams.endTime = info.endTime || '';
    };

    // 草稿数据列表
    const showDataList = computed(() => resultData.list.map((item: any) => {
        const itemInfo = {
            ...item,
            createdWording: dayjs(item.createdTime).format('YYYY-MM-DD HH:mm:ss'),
            uploadStatusInfo: (() => {
                const info = {
                    type: '',
                    text: '',
                };
                if (item.uploadStatus === 0) {
                    info.type = 'primary';
                    info.text = '待上报';
                }
                if (item.uploadStatus === 1) {
                    info.type = 'success';
                    info.text = '上报成功';
                }
                if (item.uploadStatus === 2) {
                    info.type = 'warning';
                    info.text = '上报失败';
                }
                if (item.uploadStatus === 3) {
                    info.type = 'primary';
                    info.text = '上报中';
                }
                if (item.uploadStatus === 4) {
                    info.type = 'primary';
                    info.text = '上报之后数据有修改';
                }
                if (item.uploadStatus === 5) {
                    info.type = 'info';
                    info.text = '无需上报';
                }
                return info;
            })(),
            uploadMemoWording: item.uploadMemo || '-',
            uploadTimeWording: (() => {
                if (!item.uploadTime) {
                    return '-';
                }
                return dayjs(item.uploadTime).format('YYYY-MM-DD HH:mm:ss');
            })(),
            lastModifiedWording: (() => {
                if (!item.lastModifiedTime) {
                    return '-';
                }
                return dayjs(item.lastModifiedTime).format('YYYY-MM-DD HH:mm:ss');
            })(),
            businessLastModifiedWording: (() => {
                if (!item.businessLastModified) {
                    return '-';
                }
                return dayjs(item.businessLastModified).format('YYYY-MM-DD HH:mm:ss');
            })(),
            businessTimeWording: (() => {
                if (!item.businessTime) {
                    return '-';
                }
                return dayjs(item.businessTime).format('YYYY-MM-DD HH:mm:ss');
            })(),
            sourceData: utils.jsonParseSafety(item.sourceData),
        };
        return itemInfo;
    }));

    /**
     * 创建查询参数
     * <AUTHOR>
     * @date 2025-11-06
     * @returns {Object}
     */
    const createParams = () => {
        const params = {
            regionId: toolsParams.regionId,
            uploadStatus: (() => {
                if (toolsParams.uploadStatus) {
                    return toolsParams.uploadStatus;
                }
                return '0,1,2,3,4,5';
            })(),
            taskId: toolsParams.taskId,
            startTime: toolsParams.startTime,
            endTime: toolsParams.endTime,
            offset: (toolsParams.pageNum - 1) * toolsParams.pageSize, // 偏移量
            limit: toolsParams.pageSize, // 每页条数
        };
        return params;
    };

    /**
     * 获取上传记录数据
     * <AUTHOR>
     * @date 2025-11-06
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    const fetchUploadRecordData = async (params: any) => {
        const response = await StatisticsApi.fetchUploadRecordData(params);
        if (response.status === false) {
            return response;
        }
        return response;
    };

    return {
        loadingModelTable,
        toolsParams,
        isEnableFetchData,
        resultData,
        regionOptions,
        uploadStatusOptions,
        showDataList,
        initToolsParams,
        createParams,
        fetchUploadRecordData,
    };
};