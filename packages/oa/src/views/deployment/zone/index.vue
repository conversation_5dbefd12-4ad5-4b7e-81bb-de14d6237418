<template>
    <div class="zone-task-container">
        <div class="header">
            <h2>分区放量任务管理</h2>
            <el-button type="primary" @click="showCreateDialog = true">
                创建任务
            </el-button>
        </div>

        <!-- 任务列表 -->
        <div class="table-container">
            <div v-if="taskList.length === 0 && !loading" class="empty-state">
                <p>暂无任务数据</p>
            </div>
            <el-table
                v-loading="loading"
                :data="taskList"
                border
                stripe
                style="width: 100%;"
                :row-class-name="tableRowClassName"
                @row-click="viewDetail"
            >
                <el-table-column prop="name" label="任务名称" width="350" />
                <el-table-column
                    prop="env"
                    label="环境"
                    :formatter="formatEnv"
                    width="100"
                />
                <el-table-column
                    prop="fromZone"
                    label="从"
                    :formatter="formatZone"
                    width="100"
                />
                <el-table-column
                    prop="toZone"
                    label="到"
                    :formatter="formatZone"
                    width="100"
                />
                <el-table-column
                    prop="status"
                    label="状态"
                    width="200"
                >
                    <template #default="{ row }">
                        <el-tag :type="getStatusType(row.status)" effect="light">
                            {{ formatStatus(row) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" width="200">
                    <template #default="{ row }">
                        {{ formatCreatedTime(row.created) }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="200" fixed="right">
                    <template #default="{ row }">
                        <el-button
                            v-if="canPauseTask(row.status)"
                            size="small"
                            type="warning"
                            @click.stop="pauseTask(row)"
                        >
                            停止
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <!-- 创建任务对话框 -->
        <CreateTaskDialog
            v-model="showCreateDialog"
            @success="loadTaskList"
        />

        <!-- 任务详情对话框 -->
        <TaskDetailDialog
            v-model="showDetailDialog"
            :task-id="selectedTaskId"
        />
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { HighlyAvailableAPI } from '@/api/highly-available-api';
import { regionOptions, envOptions, zoneOptions, canPauseTask, canResumeTask } from '../model/zone';
import CreateTaskDialog from './CreateTaskDialog.vue';
import TaskDetailDialog from './TaskDetailDialog.vue';

const taskList = ref<AbcAPI.CreateZoneTaskReq[]>([]);
const loading = ref(false);
const showCreateDialog = ref(false);
const showDetailDialog = ref(false);
const selectedTaskId = ref('');

const loadTaskList = async () => {
    loading.value = true;
    try {
        const res = await HighlyAvailableAPI.listZoneTasksUsingGET();
        taskList.value = res.rows || [];
    } catch (error) {
        console.error('获取任务列表失败:', error);
    } finally {
        loading.value = false;
    }
};

const formatRegion = (row: AbcAPI.CreateZoneTaskReq) => {
    try {
        return regionOptions.find(item => item.value === row.regionId)?.label || row.regionId;
    } catch (error) {
        console.error('formatRegion error:', error, row);
        return row.regionId;
    }
};

const formatEnv = (row: AbcAPI.CreateZoneTaskReq) => {
    try {
        return envOptions.find(item => item.value === row.env)?.label || row.env;
    } catch (error) {
        return row.env;
    }
};

const formatZone = (row: AbcAPI.CreateZoneTaskReq, column: any, cellValue: string) => {
    try {
        return zoneOptions.find(item => item.value === cellValue)?.label || cellValue;
    } catch (error) {
        return cellValue;
    }
};

const formatStatus = (row: AbcAPI.CreateZoneTaskReq) => {
    try {
        const statusMap = {
            10: '放量进行中',
            20: '完成',
            30: '停止',
            40: '停止并回滚',
        };
        return statusMap[row.status as keyof typeof statusMap] || row.status;
    } catch (error) {
        console.error('formatStatus error:', error, row);
        return row.status;
    }
};

const viewDetail = (row: AbcAPI.CreateZoneTaskReq) => {
    selectedTaskId.value = String(row.id);
    showDetailDialog.value = true;
};

const pauseTask = async (row: AbcAPI.CreateZoneTaskReq) => {
    await HighlyAvailableAPI.updateZoneTaskStatusUsingPUT(String(row.regionId), 30, String(row.id));
    await loadTaskList();
};

const stopAndRollbackTask = async (row: AbcAPI.CreateZoneTaskReq) => {
    await HighlyAvailableAPI.updateZoneTaskStatusUsingPUT(String(row.regionId), 40, String(row.id));
    await loadTaskList();
};

const formatCreatedTime = (created: string) => {
    if (!created) return '';
    try {
        const date = new Date(created);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    } catch (error) {
        console.error('formatCreatedTime error:', error, created);
        return created;
    }
};

const getStatusType = (status: number) => {
    const typeMap = {
        10: 'success', // 进行中 - 绿色
        20: 'info', // 完成 - 灰色
        30: 'warning', // 手动停止 - 橙色
        40: 'danger', // 停止并回退 - 红色
    };
    return typeMap[status as keyof typeof typeMap] || 'info';
};

const tableRowClassName = ({ row }: { row: AbcAPI.CreateZoneTaskReq }) => {
    if (row.status === 10) {
        return 'running-row';
    }
    return '';
};

onMounted(() => {
    loadTaskList();
});
</script>

<style scoped>
.zone-task-container {
    padding: 20px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.header h2 {
    margin: 0;
    color: #303133;
}

.table-container {
    background: white;
    border-radius: 12px;
    padding: 0;
    box-shadow: 0 8px 24px rgba(15, 29, 71, 0.08);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.table-container :deep(.el-table) {
    flex: 1;
    border: none;
}

.table-container :deep(.el-table__inner-wrapper) {
    border-radius: 12px;
}

.table-container :deep(.el-table__header-wrapper th) {
    background-color: #f5f7fb;
    color: #303133;
    font-weight: 600;
}

.table-container :deep(.el-table__cell) {
    padding: 14px 20px;
}

.empty-state {
    text-align: center;
    padding: 48px 20px;
    color: #909399;
}

.empty-state p {
    margin: 0;
    font-size: 14px;
}

:deep(.running-row) {
    background-image: linear-gradient(90deg, rgba(64, 158, 255, 0.18), rgba(64, 158, 255, 0.08));
    box-shadow: inset 4px 0 0 rgba(64, 158, 255, 0.6);
    transition: box-shadow .3s ease, background-image .3s ease;
}

:deep(.running-row:hover) {
    background-image: linear-gradient(90deg, rgba(64, 158, 255, 0.24), rgba(64, 158, 255, 0.12));
    box-shadow: inset 4px 0 0 rgba(64, 158, 255, 0.8);
}
</style>
