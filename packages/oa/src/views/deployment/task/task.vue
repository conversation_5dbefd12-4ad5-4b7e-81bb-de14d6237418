<script setup lang="ts">
import { DeploymentApi } from '@/api/deployment-api';
import { OpsAPI } from '@/api/ops-api';
import { useFormat } from '@/composables/date';
import { <PERSON> } from '@/vendor/jenkins';
import {
    DeployTagApprovalStatus,
    DeployTagStatus,
    DeployTagType,
    ExecType,
    getIngStatus,
    isBuild,
    isDeployed,
    isIngStatus,
    K8sInfo,
} from '@/views/deployment/model';
import { useDeploymentStore } from '@/views/deployment/store';
import { ElMessage } from 'element-plus/es';
import { computed, onMounted, onUnmounted, reactive, ref, watch } from 'vue';
import TagRegionStatus from '@/views/deployment/task/tag-region-status.vue';
import TagOperateBtns from '@/views/deployment/task/tag-operate-btns.vue';
import { useThemeConfigStore } from '@/store/theme-config';
import DeployTask = AbcAPI.DeployTask;
import { useUserStore } from '@/store/user';
import TagJenkinsStatus from '@/views/deployment/task/tag-jenkins-status.vue';

const props = defineProps({
    id: {
        type: String,
        required: true,
    },
});
const vmData = reactive({
    currentBusiness: '',
    rows: <any>[],
    selectedDeployMap: <Record<string, Array<string>>> {},
    // 新增：批量 zone 选择的状态管理
    selectedZoneRegionsMap: <Record<string, Record<string, Array<string>>>> {}, // {tagKey: {zoneName: [regionName]}}
    task: <DeployTask><any>null,
    // 新增：zone 维度的 k8s 信息存储
    k8sRegionZoneInfoMap: <Record<string, Record<string, K8sInfo[]>>> {}, // {region-env-zone: {serviceName: K8sInfo[]}}
    checkK8s: false,
    isLoading: true,
});
const deploymentStore = useDeploymentStore();

const deployRegionList = [
    { id: 1, name: 'region1' },
    { id: 2, name: 'region2' },
];

// region 对应的 zone 配置
const regionZoneMap: Record<string, { name: string; label: string }[]> = {
    region1: [{ name: 'standby', label: '备用区' }, { name: 'primary', label: '主用区' }],
    region2: [{ name: 'standby', label: '备用区' }, { name: 'primary', label: '主用区' }],
};

// 获取所有 region-zone 组合，按发布顺序排列
const orderedRegionZoneList = [];
for (const region of deployRegionList) {
    const zones = regionZoneMap[region.name] || [];
    for (const zone of zones) {
        orderedRegionZoneList.push({
            regionId: region.id,
            regionName: region.name,
            zoneName: zone.name,
            zoneLabel: zone.label,
            displayName: `${region.name}-${zone.label}`,
        });
    }
}

const isTaskOpen = computed(() => vmData.task?.status === 0);

function fillRegionDeployInfo(tag: any = {}, business: string, env: string) {
    let jenkinsInfo = tag.jenkinsInfo?.find((item: any) => item.business === business);

    tag.jenkinsId = jenkinsInfo ? jenkinsInfo.id : tag.jenkinsId;
    tag.jenkinsUrl = jenkinsInfo ? jenkinsInfo.url : tag.jenkinsUrl;
    tag.supportRegionDeploy = jenkinsInfo ? jenkinsInfo.supportRegionDeploy : tag.supportRegionDeploy;

    // 处理分区和可用区的状态
    tag.regionZoneStatus = {};

    // 如果 regionInfo 包含 zone 信息，则按 region-zone 组合处理
    if (tag.regionInfo?.length && tag.regionInfo[0].zone) {
        // 新的数据结构，包含 zone 信息
        tag.regionInfo.forEach((regionZone: any) => {
            const regionName = regionZone.name;
            const zoneName = regionZone.zone;
            const regionK8sInfo = vmData.k8sRegionZoneInfoMap[regionName + '-' + env + '-' + zoneName]?.[tag.serviceName];

            if (!tag.regionZoneStatus[regionName]) {
                tag.regionZoneStatus[regionName] = {};
            }

            // 计算是否发布，需要通过 needK8s 来判断
            let isPublished = false;
            if (tag.needK8s) {
                isPublished = regionK8sInfo?.every((pod: K8sInfo) => pod.Version >= tag.name);
            } else {
                // 不需要 k8s，只要部署成功，就标记为发布成功
                isPublished = isDeployed(regionZone.status);
            }

            tag.regionZoneStatus[regionName][zoneName] = {
                status: regionZone.status,
                k8sInfo: regionK8sInfo,
                isPublished,
            };
        });

        // 计算 zone 合成状态，影响发布操作按钮的状态
        const standbyRegionZone = tag.regionInfo.filter((regionZone: any) => regionZone.zone === 'standby');
        const primaryRegionZone = tag.regionInfo.filter((regionZone: any) => regionZone.zone === 'primary');

        // 取standbyRegionZone中，status 最小的一个作为该 zone 的 composeStatus
        const standbyRegionStatus = standbyRegionZone.length > 0 ? Math.min(...standbyRegionZone.map((regionZone: any) => regionZone.status)) : -999;

        // 取primaryRegionZone中，status 最小的一个作为该 zone 的 composeStatus
        const primaryRegionStatus = primaryRegionZone.length > 0 ? Math.min(...primaryRegionZone.map((regionZone: any) => regionZone.status)) : -999;

        tag.zoneComposeStatus = {
            standby: standbyRegionStatus,
            primary: primaryRegionStatus,
        };

        // 判断是否构建过，只要有一个构建过，就更新 zoneComposeStatus 至少为构建成功
        const hasBuild = tag.regionInfo.some((item: any) => isBuild(item.status) || isDeployed(item.status));
        if (hasBuild) {
            if (tag.zoneComposeStatus.standby === 0) {
                tag.zoneComposeStatus.standby = DeployTagStatus.ONLY_BUILD_SUCCESS;
            }
            if (tag.zoneComposeStatus.primary === 0) {
                tag.zoneComposeStatus.primary = DeployTagStatus.ONLY_BUILD_SUCCESS;
            }
        }

        if (tag.zoneComposeStatus.standby === -999) {
            delete tag.zoneComposeStatus.standby;
        }

        // 初始化批量 zone 选择状态
        if (tag.supportRegionDeploy) {
            initializeZoneSelection(tag, business);
        }
    }

    // 计算 region 合成状态（兼容旧逻辑）
    tag.regionComposeStatus = tag.status;
    const allRegionZoneDeployed = tag.regionInfo?.length && tag.regionInfo.every((item: any) => isDeployed(item.status));
    const anyRegionZoneBuild = tag.regionInfo?.some((item: any) => isBuild(item.status));

    if (tag.supportRegionDeploy && !isIngStatus(tag.status)) {
        if (allRegionZoneDeployed) {
            tag.regionComposeStatus = tag.status;
        } else if (anyRegionZoneBuild) {
            tag.regionComposeStatus = DeployTagStatus.ONLY_BUILD_SUCCESS;
        }
    }
}

const businessGroups = computed(() => {
    const businessGroups = vmData.rows.reduce((acc: any, cur: any) => {
        const businessList = cur.jenkinsInfo?.length ? cur.jenkinsInfo.map((item: any) => item.business) : [cur.jenkinsBusiness];
        const env = cur.env;
        const prefix = cur.prefix;

        businessList.forEach((business: any) => {
            acc[business] = acc[business] || {};
            // 再按照 env 和 prefix 分组
            acc[business][env] = acc[business][env] || {};
            acc[business][env].tagGroups = acc[business][env].tagGroups || {};
            if (business !== 'abc-his') {
                cur.k8sInfo = vmData.k8sRegionZoneInfoMap[business + '-' + env + '-primary']?.[cur.serviceName];
                cur.isPublished = cur.k8sInfo?.every((pod: K8sInfo) => pod.Version >= cur.name);
            }
            acc[business][env].tagGroups[prefix] = acc[business][env].tagGroups[prefix] || [];
            acc[business][env].tagGroups[prefix].push(cur);
        });
        return acc;
    }, {});

    const businessArr = Object.keys(businessGroups).map((business: any) => {
        const envGroups = businessGroups[business];
        const envPriorityMap = {
            pre: 1,
            gray: 2,
            prod: 3,
        };
        const envArr = Object.keys(envGroups).map((env: string) => {
            const tagGroups = envGroups[env].tagGroups;
            const pods = Object.keys(tagGroups).map((tagGroup: any) => {
                // 取最近的一个 tag，根据 name 排序
                let g = tagGroups[tagGroup].sort((a: any, b: any) => b.name - a.name);
                const [latestTag, ...rest] = g;
                // 处理备注信息，把重复 tag 的备注汇总到最新的 tag 里
                let remarks = [latestTag.remark];
                let operations = [latestTag.operation];
                let depList = [latestTag.deps];
                const children = rest.map((item: any) => {
                    remarks.push(item.remark);
                    operations.push(item.operation);
                    depList.push(item.deps);
                    return {
                        ...item,
                        isChildren: true,
                    };
                });
                remarks = remarks.filter(item => item !== '无');
                operations = operations.filter(item => item !== '无');
                depList = depList.filter(item => item !== '无');
                if (remarks.length === 0) {
                    remarks.push('无');
                }
                if (operations.length === 0) {
                    operations.push('无');
                }
                if (depList.length === 0) {
                    depList.push('无');
                }
                latestTag.remarks = remarks;
                latestTag.operations = operations;
                latestTag.depList = depList;

                fillRegionDeployInfo(latestTag, business, env);
                children.forEach((item: any) => {
                    fillRegionDeployInfo(item, business, env);
                });

                // 判断分组中是否全是 hotfix，只有全是 hotfix，才能走 hotfix 的发布流程
                latestTag.isAllHotfix = g.every((item: any) => item.isHotfix);
                return {
                    ...latestTag,
                    children,
                };
            });
            const successCount = pods.filter((pod: any) => [DeployTagStatus.BUILD_SUCCESS, DeployTagStatus.ONLY_DEPLOY_SUCCESS].includes(pod.status)).length;
            const totalCount = pods.length;
            return {
                business,
                env,
                envLabel: { pre: '预发布P', gray: '灰度G', prod: '正式V' }[env],
                priority: envPriorityMap[env as keyof typeof envPriorityMap],
                pods,
                successCount,
                totalCount,
            };
        }).sort((a:any, b: any) => a.priority - b.priority);
        return {
            business,
            envArr,
            successCount: envArr.reduce((acc, cur) => acc + cur.successCount, 0),
            totalCount: envArr.reduce((acc, cur) => acc + cur.totalCount, 0),
        };
    });

    return businessArr;
});

const businessList = computed(() => businessGroups.value.reduce((acc: any, cur: any) => {
    const envs = cur.envArr.map((env: any) => env.env);
    if (cur.business === 'abc-his') {
        // abc-his 特殊处理，同时 push region1 和 region2
        acc.push({
            name: 'region1',
            envs,
        });
        acc.push({
            name: 'region2',
            envs,
        });
    } else {
        acc.push({
            name: cur.business,
            envs,
        });
    }
    return acc;
}, []));

const businessOptions = computed(() => {
    const options = businessGroups.value.reduce((acc: any, cur: any) => {
        acc.push({
            name: cur.business,
            showTag: true,
            successCount: cur.successCount,
            totalCount: cur.totalCount,
        });
        return acc;
    }, []);
    return options;
});

watch(businessGroups, () => {
    if (!vmData.currentBusiness) {
        vmData.currentBusiness = businessOptions.value?.[0]?.name;
    }
}, {
    deep: true,
});

const jenkins = new Jenkins();
const themeConfig = useThemeConfigStore();
onMounted(async () => {
    themeConfig.setCollapse(true);
    await fetchDetail();
    // 当前处于打开状态，才轮询状态
    if (isTaskOpen.value || true) {
        await checkPublishStatus();
        fetchAllK8sInfo();
    }
    vmData.isLoading = false;
});

onUnmounted(() => {
    if (timer) {
        clearTimeout(timer);
    }
});

async function fetchDetail() {
    const task = await DeploymentApi.getApiLowCodeDeploymentDeployTaskById(props.id);
    task.tags.forEach(tag => {
        const remark = tag.remark;
        const regex = /CR审核人:(.*?)\nCR链接:(.*?)\n紧急预案:(.*?)\n备注:(.*)/;
        const matches = remark.match(regex);

        if (matches) {
            const crUser = matches[1].trim();
            const crUrl = matches[2].trim();
            const prePlan = matches[3].trim();
            const comment = matches[4].trim();

            tag.crUser = crUser;
            tag.crUrl = crUrl;
            tag.prePlan = prePlan;
            tag.remark = comment;
        }

        tag.isHotfix = tag.type === DeployTagType.HOTFIX;

        // 处理审批状态
        // tag.isApproved = tag.approvalStatus === DeployTagApprovalStatus.APPROVAL_SUCCESS || tag.approvalStatus === DeployTagApprovalStatus.NO_NEED_APPROVAL;
        tag.isApproved = true;
    });
    vmData.task = task;
    vmData.rows = task.tags;

    businessGroups.value.forEach((group: any) => {
        group.envArr.forEach((env: any) => {
            env.pods.forEach((pod: any) => {
                if (pod.supportRegionDeploy) {
                    // 为支持 region 部署的 tag 初始化选中状态
                    // 如果有 zone 数据，则初始化批量 zone 选择状态
                    // 如果没有 zone 数据，则保持原有的选择框逻辑
                    const hasZoneData = pod.regionInfo?.length && pod.regionInfo[0].zone;
                    if (hasZoneData) {
                        // 初始化批量 zone 选择状态
                        initializeZoneSelection(pod, group.business);
                    } else {
                        vmData.selectedDeployMap[group.business + pod.jenkinsId + pod.prefix + pod.name] = deployRegionList.map((item: any) => item.name);
                    }
                }
            });
        });
    });
}

async function handlePublishClick(tag: any, execType: ExecType = ExecType.PUBLISH) {
    let selectedDeployRegion = vmData.selectedDeployMap[businessGroup.value!.business + tag.jenkinsId + tag.prefix + tag.name];
    doPublish(tag, execType, selectedDeployRegion);
}

// 获取某个 region-zone 的发布状态
function getRegionZoneStatus(tag: any, region: string, zone: string) {
    return tag.regionZoneStatus?.[region]?.[zone] || null;
}

// 获取 tag 的唯一标识
function getTagKey(tag: any, business: string) {
    return business + tag.jenkinsId + tag.prefix + tag.name;
}

// 初始化批量 zone 选择状态
function initializeZoneSelection(tag: any, business: string) {
    const tagKey = getTagKey(tag, business);
    if (!vmData.selectedZoneRegionsMap[tagKey]) {
        // 使用 Vue.set 或者直接赋值来确保响应式
        vmData.selectedZoneRegionsMap[tagKey] = {
            standby: [...deployRegionList.map(r => r.name)], // 默认选中所有 region 的 standby
            primary: [...deployRegionList.map(r => r.name)], // 默认选中所有 region 的 primary
        };
    }
    return vmData.selectedZoneRegionsMap[tagKey];
}

// 检查指定 zone 是否可以发布
function canPublishZone(tag: any, zone: string) {
    // return true;
    if (zone === 'standby') {
        return true; // standby 总是可以发布
    }
    if (zone === 'primary') {
        // primary 需要所有选中的 region 的 standby 都发布成功
        const tagKey = getTagKey(tag, businessGroup.value?.business || '');
        const selectedRegions = vmData.selectedZoneRegionsMap[tagKey]?.primary || [];
        const standbyAllPublised = selectedRegions.every(region => {
            const standbyStatus = getRegionZoneStatus(tag, region, 'standby');
            // 没有备用分区，则也认为已经发布（预发布环境）
            if (standbyStatus === null) {
                return true;
            }
            return standbyStatus?.isPublished === true;
        });
        return standbyAllPublised;
    }
    return false;
}

// 批量发布指定 zone
async function handleBatchPublishZone(tag: any, zone: string, execType: ExecType = ExecType.PUBLISH) {
    const tagKey = getTagKey(tag, businessGroup.value?.business || '');
    const selectedRegions = vmData.selectedZoneRegionsMap[tagKey]?.[zone] || [];

    if (selectedRegions.length === 0) {
        ElMessage({
            type: 'error',
            message: `请至少选择一个分区的${zone === 'standby' ? '备用区' : '主用区'}`,
        });
        return;
    }

    if (!canPublishZone(tag, zone)) {
        ElMessage({
            type: 'error',
            message: zone === 'primary' ? '请先发布完成所有选中分区的备用区' : '无法发布',
        });
        return;
    }

    doPublish(tag, execType, selectedRegions, zone);
}

async function doPublish(tag: any, execType: ExecType = ExecType.PUBLISH, selectedDeployRegion: string[] = [], zone?: string) {
    if (tag.supportRegionDeploy && !selectedDeployRegion?.length) {
        ElMessage({
            type: 'error',
            message: '请至少选择一个分区发布',
        });
        return;
    }
    console.log('tag', tag.jenkinsUrl, 'execType', execType, 'deployRegion', selectedDeployRegion, 'zone', zone, tag.name);

    const publishParams: any = {
        repoTag: tag.name,
        tagName: tag.name,
        execType,
        deployRegion: selectedDeployRegion,
    };

    // 如果指定了 zone，则添加 zone 参数
    if (zone) {
        publishParams.deployZone = zone;
    }

    const response = await jenkins.triggerJob(tag.jenkinsUrl, publishParams);
    if (!response.status) {
        ElMessage({
            type: 'error',
            message: response.message || '构建失败，请确认是否正确配置 Jenkins 密钥',
        });
        return;
    }
    try {
        await DeploymentApi.putApiLowCodeDeploymentDeployTagByIdStatus({
            region: selectedDeployRegion.join(','),
            zone,
            status: getIngStatus(execType),
        }, tag.id);
        await fetchDetail();
    } catch (e: any) {
        ElMessage({
            type: 'error',
            message: e.message || '发布失败',
        });
    }
}

const userStore = useUserStore();

const canApprove = computed(() => ['bubble', 'FanYu', 'SanShao', 'hefei', 'poby', 'LiLongBin', 'SuKe'].includes(userStore.userInfo.userId));

async function handleApproveClick(tag: any) {
    try {
        await DeploymentApi.putApiLowCodeDeploymentDeployTagByIdApproved({
            approvalStatus: DeployTagApprovalStatus.APPROVAL_SUCCESS,
        }, tag.id);
        fetchDetail();
    } catch (e: any) {
        ElMessage({
            type: 'error',
            message: e.message || '审批失败',
        });
    }
}

async function handleDeleteClick(tag: any) {
    try {
        await DeploymentApi.deleteApiLowCodeDeploymentDeployTagById(tag.id);
        fetchDetail();
    } catch (e: any) {
        ElMessage({
            type: 'error',
            message: e.message || '删除失败',
        });
    }
}

const currentTime = ref(new Date());
// 判断是否是正常时间，正常时间为 6 点到 22 点，该时间段内，需要走 hotfix 流程
const isNormalTime = computed(() => {
    const hour = currentTime.value.getHours();
    return hour >= 6 && hour < 22;
});

let timer:any = null;
async function checkPublishStatus() {
    if (timer) {
        clearTimeout(timer);
    }
    timer = setTimeout(async () => {
        currentTime.value = new Date();
        await fetchDetail();
        if (vmData.checkK8s) {
            await fetchAllK8sInfo();
        }
        await checkPublishStatus();
    }, 5000);
}

function tableRowClassName({ row }: {row: any}) {
    if (!row.jenkinsId) {
        return 'warning-row';
    }
    return '';
}

async function fetchK8sServiceInfoByRegion(region: string, env: string, zone?: string) {
    const k8sInfo = await OpsAPI.getK8sServiceInfoByRegion(region, env, zone);
    const key = zone ? `${region}-${env}-${zone}` : `${region}-${env}`;

    vmData.k8sRegionZoneInfoMap[key] = k8sInfo.Pods.filter((item: K8sInfo) => !!item.ServiceName).reduce((acc: any, cur: K8sInfo) => {
        acc[cur.ServiceName] = acc[cur.ServiceName] || [];
        acc[cur.ServiceName].push(cur);
        return acc;
    }, {});
}

async function fetchAllK8sInfo() {
    const fetchArr = businessList.value.reduce((acc: any, cur: any) => {
        if (cur.name !== 'null') {
            // 新增的 zone 维度查询，如果没有 zone 维度，则默认 primary 为 zone
            const zones = regionZoneMap[cur.name] || [{ name: 'primary', label: '主用区' }];
            zones.forEach(zone => {
                acc = acc.concat(cur.envs.map((env: string) => fetchK8sServiceInfoByRegion(cur.name, env, zone.name)));
            });
        }

        return acc;
    }, []);
    return Promise.all(fetchArr);
}

const businessGroup = computed(() => {
    let businessName = vmData.currentBusiness || 'abc-his';
    if (businessName.includes('region')) {
        businessName = 'abc-his';
    }
    return businessGroups.value.find((item: any) => item.business === businessName);
});
</script>
<template>
    <h1 style="display: flex; margin-bottom: 8px;">
        <el-space>
            班车【{{ vmData.task?.name }}】
            <el-radio-group v-model="vmData.currentBusiness" class="deploy-tag-radio-group">
                <el-radio-button
                    v-for="businessOption in businessOptions"
                    :label="businessOption.name"
                >
                    <span style="line-height: 22px;">{{ businessOption.name }}</span>
                    <el-tag
                        v-if="businessOption.showTag"
                        round
                        size="small"
                        style="margin-left: 4px;"
                    >
                        {{ businessOption.successCount + '/' + businessOption.totalCount }}
                    </el-tag>
                </el-radio-button>
            </el-radio-group>
        </el-space>
        <span style="margin-left: auto;">
            <el-switch v-model="vmData.checkK8s">
            </el-switch>
            定时检查容器
        </span>
    </h1>
    <el-tabs
        v-if="businessGroup"
        :key="businessGroup.business"
        v-loading="vmData.isLoading"
        stretch
        type="border-card"
        :class="[
            'deploy-tag-tabs',
            themeConfig.isMobile ? 'is-mobile' : null,
        ]"
    >
        <el-tab-pane
            v-for="(group, index) in businessGroup.envArr"
            :key="index"
            :label="group.envLabel"
        >
            <template #label>
                <span class="custom-label">
                    {{ group.envLabel }}
                    <el-tag
                        round
                        size="small"
                        style="margin-left: 4px;"
                    >{{ group.successCount + '/' + group.totalCount }}</el-tag>
                </span>
            </template>
            <el-table
                ref="deployTable"
                :data="group.pods"
                style="width: 100%;"
                class="deploy-tag-table"
                :row-class-name="tableRowClassName"
                row-key="name"
                resizable
                border
            >
                <el-table-column
                    prop="name"
                    label="TAG"
                    width="240"
                    fixed
                >
                    <template #default="{row}">
                        <el-popover
                            :width="300"
                            trigger="click"
                            placement="right"
                            popper-class="deployment-review-popper"
                            :hide-after="0"
                        >
                            <el-form
                                class="review-wrapper"
                                size="small"
                                label-width="56px"
                                label-position="left"
                            >
                                <el-form-item label="创建人">
                                    <el-space>
                                        <span>{{ row.createdBy }}</span>
                                        <span>{{ useFormat(row.created, 'YYYY-MM-DD HH:mm') }}</span>
                                    </el-space>
                                </el-form-item>
                                <template v-if="row.crUrl">
                                    <el-form-item label="审核人" label-align="left">{{ row.crUser }}</el-form-item>
                                    <el-form-item label="CR链接" label-align="left">{{ row.crUrl }}</el-form-item>
                                    <el-form-item label="预案" label-align="left">{{ row.prePlan }}</el-form-item>
                                </template>
                            </el-form>
                            <template #reference>
                                <el-tag>
                                    <el-space>
                                        <el-icon v-if="row.type === DeployTagType.HOTFIX" color="#f68b8d"><HotWater /></el-icon>
                                        {{ row.name }}
                                    </el-space>
                                </el-tag>
                            </template>
                        </el-popover>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="deps"
                    label="依赖"
                    width="120"
                >
                    <template #default="{row}">
                        <template v-if="row.depList">
                            <p v-for="(deps, index) in row.depList" :key="index">{{ deps }}</p>
                        </template>
                        <span v-else>{{ row.deps }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="operation"
                    label="操作"
                    width="120"
                >
                    <template #default="{row}">
                        <template v-if="row.operations">
                            <p v-for="(operation, index) in row.operations" :key="index">{{ operation }}</p>
                        </template>
                        <span v-else>{{ row.operation }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="remark"
                    label="备注"
                    min-width="240"
                >
                    <template #default="{row}">
                        <template v-if="row.remarks">
                            <p v-for="(remark, index) in row.remarks" :key="index">{{ remark }}</p>
                        </template>
                        <span v-else>{{ row.remark }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="btns"
                    label="构建部署"
                    :width="themeConfig.isMobile ? 120 : 340"
                    class-name="btns-columns"
                    fixed="right"
                >
                    <template #header>
                        <el-space>
                            构建部署
                            <el-tooltip>
                                <el-icon><QuestionFilled /></el-icon>
                                <template #content>
                                    <div>
                                        <p>1. Hotfix 需要审批后才能发布</p>
                                        <p>2. 支持 region 的 tag，都需要先发布 standby 可用区，成功后才能发布 primary 可用区</p>
                                        <p>3. 一旦产生了非 Hotfix 的 tag，将回退为正常发布</p>
                                    </div>
                                </template>
                            </el-tooltip>
                        </el-space>
                    </template>
                    <template #default="{row}">
                        <!-- 2025-06-05 14:12:02 row.isChildren === false 说明是最新的 tag，为了支持回滚操作，历史 tag 也放开显示-->
                        <div v-if="row.jenkinsUrl" style="margin-bottom: 8px;">
                            <!-- 操作栏：审批                       -->
                            <el-row justify="end" style="margin-top: 8px; margin-bottom: 16px;">
                                <el-space>
                                    <tag-jenkins-status :status="row.regionComposeStatus" :jenkins-url="row.jenkinsUrl"></tag-jenkins-status>
                                </el-space>
                            </el-row>
                            <!-- 支持分区部署 -->
                            <template v-if="row.supportRegionDeploy">
                                <!-- 新的批量 zone 选择界面 -->
                                <template v-if="row.zoneComposeStatus && Object.keys(row.zoneComposeStatus).length > 0">
                                    <!-- 有 zone 数据的新界面 -->
                                    <div style="margin-bottom: 16px;">
                                        <!-- Standby Zone 发布区域 -->
                                        <div
                                            v-if="typeof row.zoneComposeStatus['standby'] !== 'undefined'"
                                            style="margin-bottom: 12px; padding: 8px; border: 1px solid #e0e0e0; border-radius: 4px;"
                                        >
                                            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                                <span style="font-weight: bold; margin-right: 12px;">备用区 (Standby)</span>
                                                <tag-operate-btns
                                                    is-prev-region-deployed
                                                    :status="row.zoneComposeStatus['standby']"
                                                    :is-approved="row.isApproved"
                                                    :tag="row"
                                                    @click-publish="(tag, execType) => handleBatchPublishZone(tag, 'standby', execType)"
                                                ></tag-operate-btns>
                                            </div>
                                            <el-checkbox-group
                                                v-model="vmData.selectedZoneRegionsMap[getTagKey(row, businessGroup.business)].standby"
                                                :disabled="!row.isApproved"
                                            >
                                                <el-row v-for="region in deployRegionList" :key="`standby-${region.id}`" style="margin-bottom: 4px;">
                                                    <el-col :span="10">
                                                        <el-checkbox :label="region.name" size="small">{{ region.name }}</el-checkbox>
                                                    </el-col>
                                                    <el-col :span="14">
                                                        <tag-region-status
                                                            :tag="row"
                                                            :status="getRegionZoneStatus(row, region.name, 'standby')?.status || 0"
                                                            :is-published="getRegionZoneStatus(row, region.name, 'standby')?.isPublished || false"
                                                            :k8s-info="getRegionZoneStatus(row, region.name, 'standby')?.k8sInfo"
                                                        ></tag-region-status>
                                                    </el-col>
                                                </el-row>
                                            </el-checkbox-group>
                                        </div>

                                        <!-- Primary Zone 发布区域 -->
                                        <div style="padding: 8px; border: 1px solid #e0e0e0; border-radius: 4px;">
                                            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                                                <span style="font-weight: bold; margin-right: 12px;">主用区 (Primary)</span>
                                                <tag-operate-btns
                                                    :is-prev-region-deployed="canPublishZone(row, 'primary')"
                                                    :status="row.zoneComposeStatus['primary']"
                                                    :is-approved="row.isApproved"
                                                    :tag="row"
                                                    @click-publish="(tag, execType) => handleBatchPublishZone(tag, 'primary', execType)"
                                                ></tag-operate-btns>
                                            </div>
                                            <el-checkbox-group
                                                v-model="vmData.selectedZoneRegionsMap[getTagKey(row, businessGroup.business)].primary"
                                                :disabled="!row.isApproved || !canPublishZone(row, 'primary')"
                                            >
                                                <el-row v-for="region in deployRegionList" :key="`primary-${region.id}`" style="margin-bottom: 4px;">
                                                    <el-col :span="10">
                                                        <el-checkbox :label="region.name" size="small">{{ region.name }}</el-checkbox>
                                                    </el-col>
                                                    <el-col :span="14">
                                                        <tag-region-status
                                                            :tag="row"
                                                            :status="getRegionZoneStatus(row, region.name, 'primary')?.status || 0"
                                                            :is-published="getRegionZoneStatus(row, region.name, 'primary')?.isPublished || false"
                                                            :k8s-info="getRegionZoneStatus(row, region.name, 'primary')?.k8sInfo"
                                                        ></tag-region-status>
                                                    </el-col>
                                                </el-row>
                                            </el-checkbox-group>
                                        </div>
                                    </div>
                                </template>
                                <!-- 兼容旧的选择式发布（无 zone 数据时显示） -->
                                <el-checkbox-group
                                    v-else
                                    v-model="vmData.selectedDeployMap[businessGroup.business + row.jenkinsId + row.prefix + row.name]"
                                    :disabled="!row.isApproved"
                                >
                                    <el-row v-for="regionOption in deployRegionList" :key="regionOption.id" style="margin-bottom: 8px;">
                                        <el-col :span="9">
                                            <el-checkbox :label="regionOption.name" size="small">{{ regionOption.name }}</el-checkbox>
                                        </el-col>
                                        <el-col :span="15">
                                            <tag-region-status
                                                :tag="row"
                                                :status="row.regionStatus?.[regionOption.name]?.status || 0"
                                                :is-published="row.regionStatus?.[regionOption.name]?.isPublished || false"
                                                :k8s-info="row.regionStatus?.[regionOption.name]?.k8sInfo"
                                            ></tag-region-status>
                                        </el-col>
                                    </el-row>
                                    <el-row style="margin-top: 16px;" justify="end">
                                        <tag-operate-btns
                                            is-prev-region-deployed
                                            :status="row.regionComposeStatus"
                                            :is-approved="row.isApproved"
                                            :tag="row"
                                            @click-publish="handlePublishClick"
                                        ></tag-operate-btns>
                                    </el-row>
                                </el-checkbox-group>
                            </template>
                            <template v-else>
                                <el-row>
                                    <el-col :span="24">
                                        <tag-region-status
                                            :tag="row"
                                            :status="row.status"
                                            :is-published="row.isPublished"
                                            :k8s-info="row.k8sInfo"
                                        ></tag-region-status>
                                    </el-col>
                                </el-row>
                                <el-row style="margin-top: 16px;" justify="end">
                                    <tag-operate-btns
                                        is-prev-region-deployed
                                        :status="row.status"
                                        :is-approved="row.isApproved"
                                        :tag="row"
                                        @click-publish="handlePublishClick"
                                    ></tag-operate-btns>
                                </el-row>
                            </template>
                        </div>
                        <!--  不是最新的 tag，只展示审批通过的状态  -->
                        <!--                        <el-row v-else-if="row.approvalStatus === DeployTagApprovalStatus.APPROVAL_SUCCESS" justify="end">-->
                        <!--                            <el-tag type="success">{{ row.approvedBy }} 已同意发布</el-tag>-->
                        <!--                        </el-row>-->
                    </template>
                </el-table-column>
                <el-table-column
                    prop="buttons"
                    label=""
                    width="40"
                    fixed="right"
                    align="center"
                >
                    <template #default="{row}">
                        <el-popconfirm :title="`确定删除${row.name}?`" @confirm="handleDeleteClick(row)">
                            <template #reference>
                                <el-button
                                    type="danger"
                                    link
                                    :disabled="!deploymentStore.hasPermissionPublish"
                                    circle
                                >
                                    <el-icon><Delete /></el-icon>
                                </el-button>
                            </template>
                        </el-popconfirm>
                    </template>
                </el-table-column>
            </el-table>
        </el-tab-pane>
    </el-tabs>
</template>

<style lang="scss">
.deploy-tag-radio-group {
    .el-radio-button__inner {
        padding: 4px 15px;
    }
}

.tencent-doc-iframe {
    width: 100%;
    min-height: calc(100vh - 97px);
    border: 1px solid var(--el-border-color);
}

.deploy-tag-tabs {
    .el-tabs__content {
        min-height: calc(100vh - 136px);
    }
}

.el-tabs.is-mobile {
    .el-tabs__content {
        padding: 0 !important;
    }
}

.deploy-tag-table {
    .el-button {
        min-height: 24PX !important;
        height: 24PX !important;
        padding-top: 4PX !important;
        padding-bottom: 4PX !important;
    }

    .icon-k8s {
        width: 16px;
    }

    &.el-table {
        .el-table__cell {
            padding: 4PX !important;

            .cell {
                padding: 0 4px;
            }

            &.btns-columns {
                padding: 0 !important;
            }
        }
    }

    .cell {
        .error {
            color: var(--oa-danger-color);
        }

        .successful {
            color: var(--el-color-success);
        }

        .warning {
            color: var(--el-color-warning);
        }

        .publishing {
            color: var(--el-color-info);
        }
    }
}

.deployment-review-popper {
    .review-wrapper {
        .el-form-item--small {
            margin-bottom: 0;
        }
    }
}
</style>
