<template>
    <el-dialog v-model="visible" title="任务详情" width="800px">
        <div v-loading="loading">
            <div v-if="!taskDetail && !loading" class="empty-state">
                <p>暂无任务详情数据</p>
            </div>

            <div v-if="taskDetail">
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="任务名称">
                        {{ taskDetail.name || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="任务ID">
                        {{ taskDetail.id || '-' }}
                    </el-descriptions-item>
                    <el-descriptions-item label="从">
                        {{ formatZone(taskDetail.fromZone) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="到">
                        {{ formatZone(taskDetail.toZone) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="状态">
                        {{ formatStatus(taskDetail.status) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="创建人">
                        {{ taskDetail.createdBy }}
                    </el-descriptions-item>
                    <el-descriptions-item label="创建时间">
                        {{ formatTime(taskDetail.created) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="完成时间">
                        {{ formatTime(taskDetail.finished) }}
                    </el-descriptions-item>
                </el-descriptions>

                <!-- 灰度步骤列表 -->
                <div v-if="taskDetail.list?.length" class="mt-4">
                    <h3>灰度步骤</h3>
                    <el-table :data="taskDetail.list">
                        <el-table-column prop="name" label="名称" width="180" />
                        <el-table-column prop="regionCount" label="比例(%)" width="60" />
                        <el-table-column prop="realRegionCount" label="数量" width="60" />
                        <el-table-column prop="started" label="开始放量时间" width="180">
                            <template #default="{ row }">
                                {{ formatTime(row.started) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="finished" label="放量完成时间" width="180">
                            <template #default="{ row }">
                                {{ formatTime(row.finished) }}
                            </template>
                        </el-table-column>
                        <el-table-column prop="status" label="状态" width="100">
                            <template #default="{ row }">
                                <el-tag :type="getStatusType(row.status)">
                                    {{ formatDetailStatus(row.status) }}
                                </el-tag>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
    </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { HighlyAvailableAPI } from '@/api/highly-available-api';
import { regionOptions, envOptions, zoneOptions } from '../model/zone';

const props = defineProps<{
  modelValue: boolean;
  taskId: string;
}>();

const emit = defineEmits<{
  'update:modelValue': [value: boolean];
}>();

const visible = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val),
});

const taskDetail = ref<AbcAPI.CreateZoneTaskReq | null>(null);
const loading = ref(false);

const loadTaskDetail = async () => {
    if (!props.taskId) return;

    loading.value = true;
    try {
        const res = await HighlyAvailableAPI.getZoneTaskUsingGET(props.taskId);
        taskDetail.value = res;
        console.log('taskDetail loaded:', taskDetail.value);
        console.log('API response:', res);
    } catch (error) {
        console.error('Failed to load task detail:', error);
        taskDetail.value = null;
    } finally {
        loading.value = false;
    }
};

const formatRegion = (regionId: number) => regionOptions.find(item => item.value === regionId)?.label || regionId;

const formatEnv = (env: number) => envOptions.find(item => item.value === env)?.label || env;

const formatZone = (zone: string) => zoneOptions.find(item => item.value === zone)?.label || zone;

const formatStatus = (status: number) => {
    const statusMap = {
        10: '进行中',
        20: '完成',
        30: '停止',
        40: '停止并回滚',
    };
    return statusMap[status as keyof typeof statusMap] || status;
};

const formatDetailStatus = (status: number) => {
    const statusMap = {
        0: '未开始',
        10: '进行中',
        20: '完成',
    };
    return statusMap[status as keyof typeof statusMap] || status;
};

const getStatusType = (status: number) => {
    const typeMap = {
        0: 'info',
        10: 'warning',
        20: 'success',
    };
    return typeMap[status as keyof typeof typeMap] || 'info';
};

const formatTime = (timeStr: string) => {
    if (!timeStr) return '-';
    try {
        const date = new Date(timeStr);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    } catch (error) {
        console.error('formatTime error:', error, timeStr);
        return timeStr || '-';
    }
};

watch(() => props.taskId, () => {
    if (props.taskId && visible.value) {
        loadTaskDetail();
    }
});

watch(visible, (val) => {
    if (val && props.taskId) {
        loadTaskDetail();
    }
});
</script>