<script setup lang="ts">
import { ListInstance } from 'vant';
import { reactive, ref, watch } from 'vue';

const props = defineProps({
    dataProvider: {
        type: Function,
        default: () => () => [],
    },
    finishedText: {
        type: String,
        default: '没有更多了',
    },
    pageSize: {
        type: Number,
        default: 10,
    },
    // 滚动条与底部距离小于 offset 时触发 load 事件
    reachBottomOffset: {
        type: Number,
        default: 100,
    },
    // 是否在初始化时立即执行滚动位置检查
    immediateCheck: {
        type: Boolean,
        default: true,
    },
    // 是否可搜索
    searchable: {
        type: Boolean,
        default: false,
    },
    // 是否显示搜索按钮
    showAction: {
        type: Boolean,
        default: false,
    },
    // 外部搜索值
    externalSearchValue: {
        type: [String, Number],
        default: '',
    },
});

const list = ref<any[]>([]);
const listRef = ref<ListInstance>();

// 是否下拉刷新
const isRefreshing = ref(false);
// 是否完成加载所有数据
const isFinished = ref(false);
// 是否正在加载数据
const isLoading = ref(false);

// 分页参数
const pageParams = reactive({
    keyword: '',
    offset: 0,
    limit: props.pageSize,
    total: 0,
});

function resetPageParams() {
    pageParams.offset = 0;
    pageParams.total = 0;
}

async function loadData() {
    // 将 loading 设置为 true，表示列表处于加载状态
    isLoading.value = true;

    const {
        rows,
        offset,
        limit,
        total,
    } = await props.dataProvider(pageParams);
    // 下拉刷新
    if (isRefreshing.value) {
        isRefreshing.value = false;
        list.value = [];
    }

    // 更新列表
    list.value = list.value.concat(rows);
    // 更新分页参数
    pageParams.offset += pageParams.limit;
    pageParams.total += total;
    // 停止加载
    isLoading.value = false;
    // 标记加载完成
    if (list.value.length >= total) {
        isFinished.value = true;
    }
}

async function handleRefresh() {
    // 清空列表数据
    isFinished.value = false;
    resetPageParams();
    list.value = [];

    // 重新加载数据
    await loadData();
}

async function handleClear() {
    // 清空列表数据
    resetPageParams();
    list.value = [];
}

async function handleSearch() {
    // 清空列表数据
    isFinished.value = false;
    resetPageParams();
    list.value = [];
    await loadData();
}

/**
 * 检查当前的滚动位置，若已滚动至底部，则会触发 load 事件
 */
async function check() {
    listRef.value?.check();
}

watch(() => props.externalSearchValue, async () => {
    await handleSearch();
});

defineExpose({
    check,
    handleRefresh,
    handleClear,
    loadData,
});

</script>
<template>
    <div>
        <form action="search" style="margin-bottom: 8px;">
            <van-search
                v-if="searchable"
                v-model="pageParams.keyword"
                placeholder="请输入关键词"
                :show-action="showAction"
                shape="round"
                @search="handleSearch"
            >
                <template #action>
                    <div @click="handleSearch">搜索</div>
                </template>
            </van-search>
        </form>
        <van-pull-refresh v-model="isRefreshing" @refresh="handleRefresh">
            <van-list
                ref="listRef"
                v-model:loading="isLoading"
                :finished="isFinished"
                :finished-text="finishedText"
                :offset="reachBottomOffset"
                :immediate-check="immediateCheck"
                @load="loadData"
            >
                <template v-for="(item, index) in list" :key="item?.id || index">
                    <slot :item="item"></slot>
                </template>
            </van-list>
        </van-pull-refresh>
    </div>
</template>

<style>
</style>
