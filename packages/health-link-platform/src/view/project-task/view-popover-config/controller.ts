/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import * as utils from '@/common/utils';

export const createViewPopoverConfigController = () => {
    /**
     * 创建验证信息
     * <AUTHOR>
     * @date 2025-09-15
     * @param {Object} config
     * @param {string} targetFormat
     * @returns {Object}
     */
    const createValidateInfos = (config: any, targetFormat: string) => {
        const template = {
            show: false,
            error: false,
            disabled: false,
            content: '',
        };
        const infos = {
            // key
            key: (() => {
                const info = utils.cloneDeep(template);
                info.show = true; // 始终显示
                if (!config.key) {
                    info.error = true;
                    info.content = 'key必填';
                }
                return info;
            })(),
            // 类型
            type: (() => {
                const info = utils.cloneDeep(template);
                info.show = true; // 始终显示
                return info;
            })(),
            // 取值方式
            templateType: (() => {
                const info = utils.cloneDeep(template);
                info.show = true; // 始终显示
                return info;
            })(),
            // 源数据key
            ref: (() => {
                const info = utils.cloneDeep(template);
                if (config.templateType !== '') {
                    // 非ref类型，不显示
                    return info;
                }
                info.show = true; // 始终显示
                if (
                    targetFormat === 'sql'
                    && config.extendData?.layer === 1
                    && config.key === 'ref'
                ) {
                    // sql类型时，key为ref时，可以不限制
                    return info;
                }
                if (config.type === 'object') {
                    // 对象类型，不限制
                    return info;
                }
                if (
                    !config.ref
                    && utils.isNone(config.defaultValue)
                    && config.extend.required === 1
                ) {
                    info.error = true;
                    info.content = 'ref或默认值必填';
                }
                return info;
            })(),
            // 入参
            variables: (() => {
                const info = utils.cloneDeep(template);
                if (config.templateType !== 'ftl') {
                    // 非ftl类型，不显示
                    return info;
                }
                info.show = true; // 始终显示
                if (
                    !config.variables
                    || config.variables.length === 0
                ) {
                    info.error = true;
                    info.content = '入参必填';
                }
                return info;
            })(),
            // ftl模版
            template: (() => {
                const info = utils.cloneDeep(template);
                if (config.templateType !== 'ftl') {
                    // 非ftl类型，不显示
                    return info;
                }
                info.show = true; // 始终显示
                if (!config.template) {
                    info.error = true;
                    info.content = '模版必填';
                }
                return info;
            })(),
            // 精度
            precision: (() => {
                const info = utils.cloneDeep(template);
                if (config.type !== 'number') {
                    // 非数字类型，不显示
                    return info;
                }
                info.show = true; // 始终显示
                return info;
            })(),
            // 最大长度
            maxLength: (() => {
                const info = utils.cloneDeep(template);
                if (config.type !== 'string') {
                    // 非字符串类型，不显示
                    return info;
                }
                info.show = true; // 始终显示
                return info;
            })(),
            // 格式
            format: (() => {
                const info = utils.cloneDeep(template);
                if (config.type !== 'date') {
                    // 非日期类型，不显示
                    return info;
                }
                info.show = true; // 始终显示
                if (!config.format) {
                    info.error = true;
                    info.content = '格式必填';
                }
                return info;
            })(),
            // 枚举列表
            enumValues: (() => {
                const info = utils.cloneDeep(template);
                if (
                    !config.enumValues
                    || config.enumValues.length === 0
                ) {
                    // 枚举列表为空，不显示
                    return info;
                }
                info.show = true; // 始终显示
                const isEmpty = config.enumValues.some((item: any) => item.value === '' || item.description === '');
                if (isEmpty) {
                    info.error = true;
                    info.content = '枚举列表必填';
                }
                return info;
            })(),
            // 是否必填
            required: (() => {
                const info = utils.cloneDeep(template);
                if (config.type === 'object') {
                    // 对象类型，不显示
                    return info;
                }
                if (config.type === 'array') {
                    // 数组类型，不显示
                    return info;
                }
                if (config.templateType === 'ftl') {
                    // ftl类型，不显示
                    return info;
                }
                info.show = true; // 始终显示
                return info;
            })(),
            // 默认值
            defaultValue: (() => {
                const info = utils.cloneDeep(template);
                if (config.type === 'object') {
                    // 对象类型，不显示
                    return info;
                }
                if (config.type === 'array') {
                    // 数组类型，不显示
                    return info;
                }
                if (config.templateType === 'ftl') {
                    // ftl类型，不显示
                    return info;
                }
                if (config.extend.required === 0) {
                    // 非必填，不显示
                    return info;
                }
                if (
                    targetFormat === 'sql'
                    && config.extendData?.layer === 1
                    && config.key === 'ref'
                ) {
                    // sql类型时，key为ref时，不显示
                    return info;
                }
                info.show = true; // 始终显示
                if (
                    !config.ref
                    && utils.isNone(config.defaultValue)
                ) {
                    info.error = true;
                    info.content = 'ref或默认值必填';
                }
                return info;
            })(),
            // 说明
            note: (() => {
                const info = utils.cloneDeep(template);
                info.show = true; // 始终显示
                if (!config.note) {
                    info.error = true;
                    info.content = '说明必填';
                }
                return info;
            })(),
        };
        return {
            error: !!Object.values(infos).find((info: any) => info.error),
            infos,
        };
    };

    return {
        createValidateInfos,
    };
};