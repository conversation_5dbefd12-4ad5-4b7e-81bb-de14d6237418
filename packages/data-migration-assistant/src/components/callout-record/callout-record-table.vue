<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { debounce } from 'lodash';
import { ElMessage, ElDialog } from 'element-plus';
import AudioPlayer from '@/components/audio-player/audio-player.vue';
import LayoutPageContainer from '@/layout/components/page-container/layout-page-container.vue';
import LayoutPageMain from '@/layout/components/page-container/layout-page-main.vue';
import LayoutPageElement from '@/layout/components/page-container/layout-page-element.vue';
import { QiyukfAPI } from '@/api/qiyukf-api.ts';
import dayjs from 'dayjs';
import { User, Microphone } from '@element-plus/icons-vue';

const props = defineProps({
    type: {
        type: String,
        default: 'keyword',
    },
    mobile: {
        type: String,
        default: '',
    },
});

const isMobile = props.type === 'mobile';

const keyword = ref(isMobile ? props.mobile : '');
const phoneRecords = ref([]);
const currentPage = ref(1);
const pageSize = ref(15);
const totalRecords = ref(0);
const loading = ref(false);

// 默认最近一个月
const beginDate = ref(dayjs().subtract(1, 'month').format('YYYY-MM-DD'));
const endDate = ref(dayjs().format('YYYY-MM-DD'));
const dateRange = ref([beginDate.value, endDate.value]);
const dateShortcuts = ref([
    {
        text: '最近一天',
        value: () => {
            const end = new Date();
            const start = new Date();
            start.setDate(start.getDate() - 1);
            return [start, end];
        },
    },
    {
        text: '最近一周',
        value: () => {
            const end = new Date();
            const start = new Date();
            start.setDate(start.getDate() - 7);
            return [start, end];
        },
    },
    {
        text: '最近一月',
        value: () => {
            const end = new Date();
            const start = new Date();
            start.setMonth(start.getMonth() - 1);
            return [start, end];
        },
    },
    {
        text: '最近三月',
        value: () => {
            const end = new Date();
            const start = new Date();
            start.setMonth(start.getMonth() - 3);
            return [start, end];
        },
    },
]);

const fetchData = async () => {
    loading.value = true;
    try {
        const offset = (currentPage.value - 1) * pageSize.value;
        let response;
        if (isMobile) {
            response = await QiyukfAPI.queryPhoneRecordByCustomerUsingGET(pageSize.value, keyword.value, offset);
        } else {
            response = await QiyukfAPI.queryPhoneRecordUsingGET(dateRange.value[0], dateRange.value[1], pageSize.value, offset, keyword.value);
        }
        phoneRecords.value = response.rows || [];
        totalRecords.value = response.total;
    } catch (error) {
        ElMessage.error('Failed to fetch phone records. Please try again later.');
        console.error('Error fetching phone records:', error);
    } finally {
        loading.value = false;
    }
};

const handlePageChange = (page: number) => {
    currentPage.value = page;
    fetchData();
};

const debouncedFetchData = debounce(fetchData, 300);

const voiceTransDialogVisible = ref(false);
const voiceTransSentences = ref<AbcAPI.Sentence[]>([]);
const voiceSummary = ref({
    questionType: '',
    customerQuestion: '',
    servicerAnswer: '',
    customerSatisfaction: '',
});

const handleVoiceToText = async (row: AbcAPI.SupportQiyuKfPhoneRecordView) => {
    try {
        if (row.transVoiceStatus === 0 || row.transVoiceStatus === 3) {
            try {
                await QiyukfAPI.transVoiceUsingPUT(row.id);
                fetchData();
                ElMessage.success('语音转文字任务已提交，请稍后刷新查看');
            } catch (error) {
                ElMessage.error('语音转文字失败');
                console.log(error);
            }
        } else if (row.transVoiceStatus === 1) {
            ElMessage.info('语音转文字处理中，请稍后');
        } else if (row.transVoiceStatus === 2) {
            // If already converted, fetch and show the text
            const textResponse = await QiyukfAPI.getVoiceTransUsingGET(row.transVoiceTaskId);
            voiceTransSentences.value = textResponse.voiceResult || [];
            voiceSummary.value = {
                questionType: textResponse.questionType,
                customerQuestion: textResponse.customerQuestion,
                servicerAnswer: textResponse.servicerAnswer,
                customerSatisfaction: textResponse.customerSatisfaction,
            };
            voiceTransDialogVisible.value = true;
        }
    } catch (error) {
        ElMessage.error('处理语音转文字时发生错误');
        console.error('Voice to text error:', error);
    }
};

onMounted(() => {
    fetchData();
});

watch(() => props.mobile, fetchData);

</script>

<template>
    <layout-page-container>
        <layout-page-main>
            <template #header>
                <layout-page-element>
                    <el-space>
                        <el-input
                            v-if="isMobile"
                            v-model="keyword"
                            class="search-input"
                            style="width: 230px;"
                            placeholder="手机号"
                            @input="debouncedFetchData"
                        />
                        <template v-else>
                            <el-input
                                v-model="keyword"
                                class="search-input"
                                style="width: 230px;"
                                placeholder="用户手机号/姓名"
                                @input="debouncedFetchData"
                            />
                            <el-date-picker
                                v-model="dateRange"
                                :clearable="false"
                                :shortcuts="dateShortcuts"
                                value-format="YYYY-MM-DD"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                @change="fetchData"
                            />
                        </template>
                    </el-space>
                </layout-page-element>
            </template>
            <layout-page-element>
                <el-table
                    v-loading="loading"
                    border
                    :data="phoneRecords"
                    style="width: 100%;"
                >
                    <!-- 呼叫时间 -->
                    <el-table-column prop="createTime" label="呼叫时间" width="158px" />
                    <!-- 合并主叫人和主叫号码 -->
                    <el-table-column label="主叫信息">
                        <template #default="{ row }">
                            <el-space class="caller-info">
                                <span class="caller-name">{{ row.userName || '-' }}</span>
                                <span class="caller-number">{{ row.from || '-' }}</span>
                            </el-space>
                        </template>
                    </el-table-column>
                    <!-- 合并被叫人和被叫号码 -->
                    <el-table-column label="被叫信息">
                        <template #default="{ row }">
                            <el-space class="callee-info">
                                <span class="callee-name">{{ row.employeeName || '-' }}</span>
                                <span class="callee-number">{{ row.to || '-' }}</span>
                            </el-space>
                        </template>
                    </el-table-column>
                    <!-- 诊所名称 -->
                    <el-table-column prop="clinicName" label="诊所名称" />
                    <!-- 会话状态 -->
                    <el-table-column prop="status" label="通话状态" width="100px" />
                    <el-table-column prop="duration" label="通话时长" width="90px" />
                    <!-- 录音 -->
                    <el-table-column prop="recordUrl" label="录音">
                        <template #default="{ row }">
                            <audio-player v-if="row.recordUrl" style="margin: 4px;" :src="row.recordUrl" />
                            <template v-else>
                                -
                            </template>
                        </template>
                    </el-table-column>
                    <!-- 语音转文字 -->
                    <el-table-column label="语音转文字" width="120px">
                        <template #default="{ row }">
                            <el-button
                                v-if="row.transVoiceStatus === 0 || row.transVoiceStatus === 3"
                                type="primary"
                                size="small"
                                @click="handleVoiceToText(row)"
                            >
                                转换文字
                            </el-button>
                            <el-button
                                v-else-if="row.transVoiceStatus === 1"
                                type="info"
                                size="small"
                                disabled
                            >
                                转换中
                            </el-button>
                            <el-button
                                v-else-if="row.transVoiceStatus === 2"
                                type="success"
                                size="small"
                                @click="handleVoiceToText(row)"
                            >
                                查看文字
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-pagination
                    :current-page="currentPage"
                    :page-size="pageSize"
                    layout="total, prev, pager, next"
                    :total="totalRecords"
                    @current-change="handlePageChange"
                />
            </layout-page-element>
        </layout-page-main>
    </layout-page-container>
    <el-dialog
        v-model="voiceTransDialogVisible"
        title="通话记录"
        width="600px"
        destroy-on-close
        custom-class="voice-trans-dialog"
    >
        <div v-if="voiceTransSentences.length === 0" class="no-content">
            暂无转换文本
        </div>
        <el-scrollbar v-else class="voice-trans-content">
            <div v-if="voiceSummary.customerQuestion" class="voice-trans-summary">
                <div class="voice-trans-summary-item">
                    <label>问题类型</label>
                    {{ voiceSummary.questionType }}
                </div>
                <div class="voice-trans-summary-item">
                    <label>问题</label>
                    {{ voiceSummary.customerQuestion }}
                </div>
                <div class="voice-trans-summary-item">
                    <label>回答</label>
                    {{ voiceSummary.servicerAnswer }}
                </div>
                <div class="voice-trans-summary-item">
                    <label>满意度</label>
                    {{ voiceSummary.customerSatisfaction }}
                </div>
            </div>
            <div
                v-for="(sentence, index) in voiceTransSentences"
                :key="index"
                class="sentence-item"
                :class="{
                    'left-message': sentence.ChannelId === '1',
                    'right-message': sentence.ChannelId === '0'
                }"
            >
                <div class="message-avatar">
                    <el-icon v-if="sentence.ChannelId === '0'" :size="40" class="caller-icon">
                        <User />
                    </el-icon>
                    <el-icon v-else :size="40" class="callee-icon">
                        <Microphone />
                    </el-icon>
                </div>
                <div class="message-content">
                    <div class="message-text">{{ sentence.Text }}</div>
                </div>
            </div>
        </el-scrollbar>
    </el-dialog>
</template>

<style lang="scss">
.voice-trans-content {
    max-height: 500px;
    overflow-y: auto;
    padding: 16px;
    background-color: #f5f7fa;
    display: flex;
    flex-direction: column;

    .voice-trans-summary {
        background-color: var(--oa-white);
        border: 1px solid var(--oa-border-color);
        border-radius: var(--oa-border-radius-4);
        padding: 34px 12px 12px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, .04);
        line-height: 1.5;
        color: var(--oa-text-color);
        position: relative;
        overflow: hidden;

        &::before {
            content: '通话总结';
            position: absolute;
            top: 0;
            left: 0;
            background-color: var(--oa-gray-7);
            color: var(--oa-white);
            padding: 2px 12px;
            border-radius: var(--oa-border-radius-4) 0 var(--oa-border-radius-4) 0;
            font-size: 12px;
            font-weight: 600;
            box-shadow: 0 2px 3px rgba(0, 0, 0, .04);
        }

        &-item {
            display: flex;
            gap: 8px;

            label {
                width: 56px;
                flex-shrink: 0;
                font-weight: 600;
                margin-right: 4px;
                color: var(--oa-text-color);
            }
        }
    }

    .sentence-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 16px;
        max-width: 100%;

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 12px;
            flex-shrink: 0;

            .el-icon {
                color: white;
                border-radius: 50%;
                padding: 8px;
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        .message-content {
            max-width: calc(100% - 120px);
            min-width: 50px;

            .message-text {
                border-radius: 12px;
                padding: 10px 14px;
                line-height: 1.6;
                word-break: break-word;
                box-shadow: 0 2px 4px rgba(0, 0, 0, .06);
                position: relative;
                font-size: 14px;
                max-width: 100%;
            }
        }

        &.left-message {
            flex-direction: row;
            // align-self: flex-start;

            .message-avatar {
                .callee-icon {
                    background-color: #07c160;
                }
            }

            .message-content {
                .message-text {
                    background-color: white;
                    color: #303133;
                    margin-right: auto;

                    &::before {
                        content: '';
                        position: absolute;
                        top: 12px;
                        left: -8px;
                        border-style: solid;
                        border-width: 6px 8px 6px 0;
                        border-color: transparent white transparent transparent;
                    }
                }
            }
        }

        &.right-message {
            flex-direction: row-reverse;

            .message-avatar {
                .caller-icon {
                    background-color: #07c160;
                }
            }

            .message-content {
                .message-text {
                    background-color: #e6f3ff;
                    color: #303133;
                    margin-left: auto;

                    &::before {
                        content: '';
                        position: absolute;
                        top: 12px;
                        right: -8px;
                        border-style: solid;
                        border-width: 6px 0 6px 8px;
                        border-color: transparent transparent transparent #e6f3ff;
                    }
                }
            }
        }
    }

    .no-content {
        text-align: center;
        color: #909399;
        padding: 20px;
    }
}

.voice-trans-dialog {
    .el-dialog__body {
        padding: 0;
    }
}
</style>
