<template>
    <el-dialog
        v-model="isShowDialogDetailData"
        title="上报数据明细"
        width="1200px"
        class="dialog-detail-data"
    >
        <div class="tools-warpper">
            <el-select
                v-model="toolsParams.regionId"
                placeholder="请选择区域"
                style="width: 120px;"
                @change="onChangeToolsParams"
            >
                <el-option
                    v-for="item in regionOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
            <el-select
                v-model="toolsParams.uploadStatus"
                style="width: 180px;"
                clearable
                placeholder="默认全部状态"
                @change="onChangeToolsParams"
            >
                <el-option
                    v-for="item in uploadStatusOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                />
            </el-select>
            <div class="track-box"></div>
        </div>
        <div class="content-wrapper">
            <el-table
                v-loading="loadingModelTable.loading.value"
                :data="showDataList"
                :height="530"
                style="width: 100%;"
            >
                <el-table-column
                    prop="id"
                    label="ID"
                    min-width="180"
                    fixed="left"
                />
                <el-table-column
                    prop="uploadStatus"
                    label="上报状态"
                    min-width="160"
                    fixed="left"
                >
                    <template #default="scope">
                        <el-tag
                            :type="scope.row.uploadStatusInfo.type"
                        >
                            {{ scope.row.uploadStatusInfo.text }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="uploadMemoWording"
                    label="上报备注"
                    min-width="180"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="uploadTimeWording"
                    label="上报时间"
                    min-width="180"
                />
                <el-table-column
                    prop="lastModifiedWording"
                    label="最近修改时间"
                    min-width="180"
                />
                <el-table-column
                    prop="createdWording"
                    label="创建时间"
                    min-width="180"
                />
                <el-table-column
                    prop="clinicId"
                    label="门诊ID"
                    min-width="260"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="businessId"
                    label="业务ID"
                    min-width="260"
                />
                <el-table-column
                    prop="businessLastModifiedWording"
                    label="业务最近修改时间"
                    min-width="180"
                />
                <el-table-column
                    prop="businessTimeWording"
                    label="业务创建时间"
                    min-width="180"
                />
                <el-table-column
                    prop="result"
                    label="操作"
                    width="100"
                    fixed="right"
                >
                    <template #default="scope">
                        <div style="display: flex; align-items: center;">
                            <el-popover
                                :width="460"
                                trigger="hover"
                                placement="left"
                                popper-style="max-height: 560px; overflow-y: auto;"
                            >
                                <template #reference>
                                    <el-button
                                        type="text"
                                        :disabled="!scope.row.sourceData"
                                        @dblclick="onClickCopy(scope.row.sourceData)"
                                    >
                                        来源数据
                                    </el-button>
                                </template>
                                <template #default>
                                    <div>
                                        <el-text>可双击拷贝数据！！！</el-text>
                                        <vue-json-viewer
                                            :value="scope.row.sourceData"
                                            :expand-depth="5"
                                            style="margin-top: 10px;"
                                            @dblclick="onClickCopy(scope.row.sourceData)"
                                        />
                                    </div>
                                </template>
                            </el-popover>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <div class="footer-box">
                <el-pagination
                    v-model:current-page="toolsParams.pageNum"
                    :page-size="toolsParams.pageSize"
                    :total="resultData.total"
                    background
                    layout="total, prev, pager, next"
                    @change="onChangePageParams"
                />
            </div>
        </div>
    </el-dialog>
</template>

<script lang="ts" setup>
import VueJsonViewer from 'vue-json-viewer';
import { computed, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { CopyDocument } from '@element-plus/icons-vue';
import { createDetailDataController } from './controller';
import * as utils from '@/common/utils';

const props = defineProps({
    modelValue: {
        type: Number,
        default: 0,
    },
    info: {
        type: Object,
        required: true,
    },
});

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
    'select', // 选择
]);

const isShowDialogDetailData = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});

const {
    loadingModelTable,
    toolsParams,
    isEnableFetchData,
    resultData,
    regionOptions,
    uploadStatusOptions,
    showDataList,
    initToolsParams,
    createParams,
    fetchUploadRecordData,
} = createDetailDataController();

onMounted(() => {
    initToolsParams(props.info);
    fetchDataList();
});

/**
 * 获取明细数据列表
 * <AUTHOR>
 * @date 2025-11-06
 */
const fetchDataList = async () => {
    if (isEnableFetchData.value === false) {
        return false;
    }
    loadingModelTable.setLoading(true);
    const params = createParams();
    const response = await fetchUploadRecordData(params);
    if (!utils.isEqual(params, createParams())) {
        return;
    }
    loadingModelTable.setLoading(false);
    if (response.status === false) {
        resultData.list = [];
        resultData.total = 0;
        return ElMessage.error(response.message || '获取明细数据失败');
    }
    resultData.list = response.data?.rows || [];
    resultData.total = response.data?.total || 0;
};

/**
 * 工具参数改变时触发
 * <AUTHOR>
 * @date 2025-11-06
 */
const onChangeToolsParams = () => {
    toolsParams.pageNum = 1;
    fetchDataList();
};

/**
 * 分页参数改变时触发
 * <AUTHOR>
 * @date 2025-11-06
 */
const onChangePageParams = () => {
    fetchDataList();
};

/**
 * 点击拷贝来源数据
 * <AUTHOR>
 * @date 2025-11-06
 * @param {Object} data
 */
const onClickCopy = (data: any) => {
    if (!data) {
        return ElMessage.error('拷贝失败');
    }
    utils.copy(JSON.stringify(data, null, 2));
    ElMessage.success('拷贝成功');
};
</script>

<style lang="scss">
    .dialog-detail-data {
        .el-dialog__body {
            padding: 0 !important;
        }
    }
</style>