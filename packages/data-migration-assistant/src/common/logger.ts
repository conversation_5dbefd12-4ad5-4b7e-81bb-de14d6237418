const logger = window.electron?.logger || {
    info: console.log,
    warn: console.warn,
    error: console.error,
};

export function info(...args) {
    logger.info(' [cs-workbench] ', ...args);
    console.log(...args);
}

export function warn(...args) {
    logger.warn(' [cs-workbench] ', ...args);
    console.warn(...args);
}

export function error(...args) {
    logger.error(' [cs-workbench] ', ...args);
    console.error(...args);
}

export default {
    info,
    warn,
    error,
};
