<script setup>
import { computed, ref } from 'vue';

const props = defineProps({
    color: {
        type: String,
        default: 'black',
    },
});

const height = ref(100);

const path = computed(() => {
    const x = 6, y = 0;
    const p1 = [x - 4, y];
    const p2 = [x - 2, y + height.value / 4];
    const middle = [x - 6, y + height.value / 2];
    return (
        `M${x},${y} `
        + `Q${p1[0]},${p1[1]} ${p2[0]},${p2[1]} T${middle[0]},${middle[1]}`
        + `M${middle[0]},${middle[1]} `
        + `Q${x},${y + height.value / 2} ${p2[0]},${p2[1] + height.value / 2} T${x},${
            y + height.value
        }`
    );
});
</script>

<template>
    <div class="brace-wrapper">
        <svg
            class="brace"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0, 0, 6, 100"
            preserveAspectRatio="none"
        >
            <path
                :d="path"
                :stroke="color"
                fill="transparent"
                stroke-width="1"
                vector-effect="non-scaling-stroke"
            />
        </svg>
    </div>
</template>
<style>
.brace-wrapper {
    display: flex;
    align-items: center;
}

.brace {
    height: 90%;
    width: 14px;
}
</style>
