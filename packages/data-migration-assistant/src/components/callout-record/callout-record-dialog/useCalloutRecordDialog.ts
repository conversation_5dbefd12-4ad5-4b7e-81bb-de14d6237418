import { ref } from 'vue';

export function useCalloutRecordDialog() {
    const dialogVisible = ref(false);
    const customerMobile = ref('');

    const openCalloutRecordDialog = (mobile: string) => {
        customerMobile.value = mobile;
        dialogVisible.value = true;
    };

    const closeCalloutRecordDialog = () => {
        dialogVisible.value = false;
    };

    return {
        dialogVisible,
        customerMobile,
        openCalloutRecordDialog,
        closeCalloutRecordDialog,
    };
}
