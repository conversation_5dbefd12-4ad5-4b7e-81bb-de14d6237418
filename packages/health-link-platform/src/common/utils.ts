/*
 * <AUTHOR>
 * @DateTime 2023-11-14 17:17:50
 */
import _ from 'lodash';
import dayjs from 'dayjs';
import OSS from './oss';
import { ElMessageBox } from 'element-plus';
import AbcResponse from './AbcResponse';
import { XMLParser, XMLBuilder } from 'fast-xml-parser';

declare global {
    interface Window {
        // 远程代码执行SDK
        remoteSDK: any;
        // 是否God模式
        isGodMode: boolean;
    }
}

// 深拷贝
export const cloneDeep = _.cloneDeep;

// 判断两个对象是否相等
export const isEqual = _.isEqual;

// 防抖
export const debounce = _.debounce;

/**
 * 加法函数
 * <AUTHOR>
 * @date 2022-05-11
 * @returns {Number}
 */
export const add = (...args: any[]) => {
    if (args.length === 0) {
        return 0;
    }
    if (args.length === 1) {
        return args[0];
    }
    if (args.length === 2) {
        let [a, b] = args;
        if (!a) a = 0;
        if (!b) b = 0;
        a += '';
        b += '';
        let al = a.split('.')[1];
        let bl = b.split('.')[1];
        al = al ? al.length : 0;
        bl = bl ? bl.length : 0;
        let c = Math.abs(al - bl);
        // eslint-disable-next-line no-restricted-properties
        let m = Math.pow(10, Math.max(al, bl));
        if (c > 0) {
            // eslint-disable-next-line no-restricted-properties
            const cm = Math.pow(10, c);
            if (al > bl) {
                a = Number(a.replace('.', ''));
                b = Number(b.replace('.', '')) * cm;
            } else {
                a = Number(a.replace('.', '')) * cm;
                b = Number(b.replace('.', ''));
            }
        } else {
            a = Number(a.replace('.', ''));
            b = Number(b.replace('.', ''));
        }
        return (a + b) / m;
    }
    return args.reduce((a, b) => add(a, b));
};

/**
 * 减法函数
 * <AUTHOR>
 * @date 2022-05-11
 * @returns {Number}
 */
export const red = (...args: any[]) => {
    if (args.length === 0) {
        return 0;
    }
    if (args.length === 1) {
        return args[0];
    }
    if (args.length === 2) {
        let [a, b] = args;
        if (!a) a = 0;
        if (!b) b = 0;
        a += '';
        b += '';
        return add(a, -b);
    }
    return args.reduce((a, b) => red(a, b));
};

/**
 * Description: 创建日期快捷选择
 * <AUTHOR>
 * @date 2025-11-06
 * @returns {Object}
 */
export const createShortcuts = () => {
    const shortcuts = [
        {
            text: '今天',
            value: () => {
                const date = dayjs();
                const s = date.startOf('day').toDate();
                const e = date.endOf('day').toDate();
                return [s, e];
            },
        },
        {
            text: '昨天',
            value: () => {
                const date = dayjs().subtract(1, 'day');
                const s = date.startOf('day').toDate();
                const e = date.endOf('day').toDate();
                return [s, e];
            },
        },
        {
            text: '本月',
            value: () => {
                const date = dayjs();
                const s = date.startOf('month').toDate();
                const e = date.endOf('month').toDate();
                return [s, e];
            },
        },
        {
            text: '上月',
            value: () => {
                const date = dayjs().subtract(1, 'month');
                const s = date.startOf('month').toDate();
                const e = date.endOf('month').toDate();
                return [s, e];
            },
        },
    ];
    return shortcuts;
};

/**
 * 计算百分比
 * @param {number} part - 部分值
 * @param {number} total - 总值
 * @param {number} [decimals=2] - 保留的小数位数（默认2位）
 * @returns {number} 百分比结果
 */
export const calculatePercentage = (part: number, total: number, decimals = 2) => {
    const partNum = Number(part);
    const totalNum = Number(total);
    if (Number.isNaN(partNum)) return 0;
    if (Number.isNaN(totalNum)) return 0;
    if (partNum === 0) return 0; // 避免除以0
    if (totalNum === 0) return 0; // 避免除以0
    // 步骤：(部分/总值) × 100，再四舍五入
    return _.round(_.multiply(_.divide(partNum, totalNum), 100), decimals);
};

// json的parse方法，安全的
export const jsonParseSafety = (json: any) => {
    try {
        return JSON.parse(json);
    } catch (error) {
        return null;
    }
};

/**
 * 上传文件
 * <AUTHOR>
 * @date 2025-10-15
 * @param {File} file
 * @returns {Promise<AbcResponse>}
 */
export const uploadFile = async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    let uploadResponse: any = null;
    try {
        const res = await OSS.upload({
            bucket: import.meta.env.VITE_APP_OSS_BUCKET,
            region: import.meta.env.VITE_APP_OSS_REGION,
            rootDir: 'oa/health-link-platform/file',
        }, file);
        uploadResponse = AbcResponse.success({
            filename: file.name,
            type: file.type,
            ...res,
        });
    } catch (error: any) {
        console.error('file 上传失败', error);
        uploadResponse = AbcResponse.error(error.message);
    }
    return uploadResponse;
};

/**
 * 格式化数字
 * 整型数字长度6位，需要这种格式：0000001
 * <AUTHOR>
 * @date 2021-06-03
 * @param {String|Number} value 数字
 * @param {Number} size 数字
 * @returns {String}
 */
export const formatFixSize = (value: string, size: number) => {
    let valueStr = (() => {
        const valueNum = parseInt(value);
        if (Number.isNaN(valueNum)) {
            return '0';
        }
        return valueNum + '';
    })();
    while (valueStr.length < size) {
        valueStr = '0' + valueStr;
    }
    return valueStr;
};

/**
 * json转xml
 * <AUTHOR>
 * @date 2025-08-29
 * @param {Object} json
 * @returns {String}
 */
export const json2xml = (json: string) => {
    const options = {
        format: true, // 启用格式化
        indentBy: '  ', // 缩进字符（2个空格）
        suppressEmptyNode: true, // 保留空节点
        ignoreAttributes: true, // 保留属性
        attributesGroupName: '$', // 属性集合名称
    };
    const builder = new XMLBuilder(options);
    return builder.build(json);
};

/**
 * xml转json
 * <AUTHOR>
 * @date 2025-08-29
 * @param {String} xml
 * @returns {Object}
 */
export const xml2json = (xml: string) => {
    const options = {
        ignoreAttributes: false, // 不忽略标签的属性
        attributesGroupName: '$', // 属性集合名称
        attributeNamePrefix: '', // 属性不要前缀
        parseTagValue: false, // 不解析标签的值
        allowBooleanAttributes: true, // 允许布尔属性
        trimValues: true, // 去除文本前后空格
    };
    const parser = new XMLParser(options);
    return parser.parse(xml);
};

/**
 * 解析json
 * <AUTHOR>
 * @date 2025-09-24
 * @param {string} jsonStr
 * @returns {any}
 */
export const parseJson = (jsonStr: string) => {
    let json = jsonStr.trim();
    if (json.startsWith('"') && json.endsWith('"')) {
        json = json.slice(1, -1);
    }
    if (json.startsWith('\'') && json.endsWith('\'')) {
        json = json.slice(1, -1);
    }
    let data = null;
    try {
        data = JSON.parse(json);
    } catch (error) {
        data = null;
    }
    if (data !== null) {
        return data;
    }
    try {
        // eslint-disable-next-line no-eval
        eval(`data = ${jsonStr}`);
        if (!isObject(data)) {
            data = null;
        }
    } catch (error) {
        data = null;
    }
    return data;
};

/**
 * 弹窗确认框，返回Promise对象，可用await接收结果
 * <AUTHOR>
 * @date 2023-11-15
 * @param {any} ...args
 * @returns {Promise<AbcResponse>}
 */
export const messageConfirm = async (...args: [string, string, any]): Promise<AbcResponse> => await new Promise((resolve) => {
    ElMessageBox.confirm(...args)
                    .then(() => {
                        resolve(AbcResponse.success());
                    })
                    .catch((action: string) => {
                        resolve(AbcResponse.error(action));
                    });
});

/**
 * 延迟Promise
 * <AUTHOR>
 * @date 2025-10-13
 * @param {number} ms
 * @returns {Promise<void>}
 */
export const delayPromise = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export function moneyStr(number: number, prepend?: string) {
    number = number || 0;
    let moneyStr = (Math.round(number * 100) / 100)
                    .toFixed(2)
                    .toString()
                    .replace(/^-?\d+/g, m => m.replace(/(?=(?!\b)(\d{3})+$)/g, ''));
    if (prepend) {
        if (moneyStr.startsWith('-')) {
            moneyStr = `-${prepend}${moneyStr.slice(1)}`;
        } else {
            moneyStr = `${prepend}${moneyStr}`;
        }
    }
    return moneyStr;
}

export function noop() {

}

export function isDef(val: any) {
    return typeof val !== 'undefined';
}

export function isFn(val: any) {
    return typeof val === 'function';
}

export function isArray(val: any) {
    return Array.isArray(val);
}

export function isString(val: any) {
    return typeof val === 'string';
}

export function isNumber(val: any) {
    return typeof val === 'number';
}

export function isBoolean(val: any) {
    return typeof val === 'boolean';
}

export function isPlainObject(val: any) {
    return Object.prototype.toString.call(val) === '[object Object]';
}

export function isObject(val: any) {
    return val !== null && typeof val === 'object';
}

export function isNone(val: any) {
    return val === null || val === undefined || val === '';
}

export function isEmptyValue(value: unknown) {
    if (Array.isArray(value)) {
        return !value.length;
    }
    if (isObject(value) && JSON.stringify(value) === '{}') {
        return true;
    }
    if (value === 0) {
        return false;
    }
    return !value;
}

export async function sleep(ms: number) {
    return new Promise(resolve => {
        setTimeout(() => {
            resolve(true);
        }, ms);
    });
}

export function clone(obj: any): any {
    if (obj == null || typeof obj != 'object') return obj;
    if (obj instanceof Array) {
        let copy = [];
        for (let i = 0, len = obj.length; i < len; ++i) {
            copy[i] = clone(obj[i]);
        }
        return copy;
    }
    if (obj instanceof Object) {
        let copy:Record<string, any> = {};
        for (let attr in obj) {
            if (obj.hasOwnProperty(attr)) copy[attr] = clone(obj[attr]);
        }
        return copy;
    }
}

export function keys<T>(o: T) {
    return Object.keys(o) as (keyof T)[];
}

export const dataURLToImage = (dataURL: string) => new Promise((resolve) => {
    const img: any = new Image();
    img.onload = () => resolve(img);
    img.src = dataURL;
});

export const canvasToFile = (canvas: HTMLCanvasElement, type: string, name: string, quality = 1): Promise<File | null> => new Promise(
    resolve => canvas.toBlob((blob: Blob | null) => {
        if (blob) {
            const file = new window.File([blob], name, { type });
            resolve(file);
        }
        resolve(null);
    }, type, quality),
);

export function flatDeepTree(source: any[], idKey = 'id', childKey = 'children') {
    const treeMap: any = new Map();
    let result: any[] = [];
    if (!source || !source.length) {
        return [];
    }
    source.forEach((item: any) => {
        treeMap.set(item[idKey], item);
        if (item[childKey] && item[childKey].length) {
            result = result.concat(flatDeepTree(item[childKey], idKey, childKey));
        }
    });
    result = result.concat(Array.from(treeMap.values()));
    return result;
}

export const dataURLtoFile = (dataUrl: string, filename?: string) => {
    let arr = dataUrl.split(',');
    let mime = arr?.[0]?.match(/:(.*?);/)?.[1];
    let bstr = atob(arr[1]), n = bstr.length;
    let u8arr = new Uint8Array(n);
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename || '', { type: mime });
};

export const encodeQueryObject = (obj: any, excludeEmpty = false) => {
    if (!isPlainObject(obj)) {
        return '';
    }
    let str = '?';
    for (const key in obj) {
        if (obj[key] || obj[key] === 0 || excludeEmpty) {
            str += ((str.length === 1 ? '' : '&') + `${key}=${obj[key] ?? ''}`);
        }
    }
    return str;
};

/**
 * @description: 判断两个数组是否有相同的值
 * @date: 2024-06-13 14:49:13
 * @author: Horace
 * @param {any[]} array1
 * @param {any[]} array2
 * @return
*/
export function hasCommonValue(array1: any[], array2: any[]) {
    if (array1.length === 0 || array2.length === 0) {
        return false;
    }

    if (typeof array1[0] === 'object' && typeof array2[0] === 'object') {
        array1 = array1.map(item => JSON.stringify(item));
        array2 = array2.map(item => JSON.stringify(item));
    }

    return array1.some(item => array2.includes(item));
}
/**
 * @description: 解决IOS端时间格式问题
 * @date: 2024-12-27 16:14:18
 * @author: Horace
 * @param {any} date
 * @return
*/
export function newDate(date?: any): Date {
    // 判定时间为ISO格式
    if (date && typeof date === 'string' && date.match(/T/)) {
        const isoDate = new Date(date);
        if (!Number.isNaN(isoDate.getTime())) {
            return isoDate;
        }
        const isoDateArr = date.split(/[-T:+]/).map((item) => parseInt(item, 10));
        if (isoDateArr.length === 7) {
            return new Date(
                isoDateArr[0],
                isoDateArr[1] - 1,
                isoDateArr[2],
                isoDateArr[3],
                isoDateArr[4],
                isoDateArr[5],
            );
        }
    }
    if (date && typeof date === 'string') {
        // 将横杠替换为斜杠
        date = date.replace(/-/g, '/');
    }
    if (!date) {
        return new Date();
    }
    return new Date(date);
}
/**
 * @description: 格式化日期
 * @date: 2024-07-18 11:35:00
 * @author: Horace
 * @param {Date | string | number} date 时间
 * @param {string} fmt 格式
 * @return
*/
export function formatDate(date: Date | string | number, fmt = 'YYYY-MM-DD HH:mm:ss'): string {
    if (!date) {
        return '';
    }

    date = newDate(date);
    const obj: any = {
        'Y+': date.getFullYear(),
        'M+': date.getMonth() + 1,
        'D+': date.getDate(),
        'H+': date.getHours(),
        'h+': date.getHours() % 12 === 0 ? 12 : date.getHours() % 12,
        'm+': date.getMinutes(),
        's+': date.getSeconds(),
        'S+': date.getMilliseconds(),
    };

    const week = ['天', '一', '二', '三', '四', '五', '六'];

    if (/(Y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
    }

    if (/(w+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (RegExp.$1.length > 1 ? '星期' : '周') + week[date.getDay()]);
    }

    for (const k in obj) {
        if (new RegExp(`(${k})`).test(fmt)) {
            fmt = fmt.replace(
                RegExp.$1,
                RegExp.$1.length === 1 ? obj[k] + '' : ('00' + obj[k]).substr(('' + obj[k]).length),
            );
        }
    }

    return fmt;
}

/**
 * 动态加载 script，由于本工程不兼容 IE，所以该实现没有 IE 实现
 * @param url
 * @param id
 */
export async function loadScript(url: string, id?: string) {
    return new Promise((resolve, reject) => {
        const head = document.getElementsByTagName('head')[0];
        const script = document.createElement('script');
        script.type = 'text/javascript';
        if (id) {
            script.id = id;
        }
        script.onload = function () {
            resolve(true);
        };
        script.onerror = function () {
            reject();
        };
        script.src = url;
        head.appendChild(script);
    });
}

/**
 * 根据 id 移除 script
 * @param id
 */
export async function unloadScript(id: string) {
    const script = document.querySelector(`#${id}`);
    script?.parentNode?.removeChild(script);
}

export function copy(value: string, cb?: Function) {
    // 动态创建 textarea 标签
    const textarea: any = document.createElement('textarea');
    // 将该 textarea 设为 readonly 防止 iOS 下自动唤起键盘，同时将 textarea 移出可视区域
    textarea.readOnly = 'readonly';
    textarea.style.position = 'absolute';
    textarea.style.left = '-9999px';
    // 将要 copy 的值赋给 textarea 标签的 value 属性
    // 网上有些例子是赋值给innerText,这样也会赋值成功，但是识别不了\r\n的换行符，赋值给value属性就可以
    textarea.value = value;
    // 将 textarea 插入到 body 中
    document.body.appendChild(textarea);
    // 选中值并复制
    textarea.select();
    textarea.setSelectionRange(0, textarea.value.length);
    document.execCommand('Copy');
    document.body.removeChild(textarea);
    if (cb && Object.prototype.toString.call(cb) === '[object Function]') {
        cb();
    }
}
