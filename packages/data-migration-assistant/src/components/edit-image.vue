<script lang="ts" setup>
import { ref, onMounted, PropType } from 'vue';

interface IDefaultOption {
    img?: string; // 裁剪图片的地址
    info?: boolean; // 裁剪框的大小信息
    outputSize?: number; // 裁剪生成图片的质量
    outputType?: string; // 裁剪生成图片的格式
    canScale?: boolean; // 图片是否允许滚轮缩放
    autoCrop?: boolean; // 是否默认生成截图框
    autoCropWidth?: number; // 默认生成截图框宽度
    autoCropHeight?: number; // 默认生成截图框高度
    fixedBox?: boolean; // 固定截图框大小 不允许改变
    fixed?: boolean; // 是否开启截图框宽高固定比例
    fixedNumber?: Array<number>; // 截图框的宽高比例
    full?: boolean; // 是否输出原图比例的截图
    canMoveBox?: boolean; // 截图框能否拖动
    original?: boolean; // 上传图片按照原始比例渲染
    centerBox?: boolean; // 截图框是否被限制在图片里面
    infoTrue?: boolean; // true 为展示真实输出图片宽高 false 展示看到的截图框宽高
    mode?: string;
}

const emit = defineEmits(['confirm', 'cancel']);
const props = defineProps({
    options: {
        type: Object as PropType<IDefaultOption>,
    },
    blob: {
        type: String,
        default: '',
    },
});

const btnLoading = ref(false);
const innerOption = ref<IDefaultOption>({
    img: '', // 裁剪图片的地址
    info: true, // 裁剪框的大小信息
    outputSize: 1, // 裁剪生成图片的质量 jpeg | png | webp
    outputType: 'png', // 裁剪生成图片的格式
    canScale: true, // 图片是否允许滚轮缩放
    autoCrop: true, // 是否默认生成截图框
    autoCropWidth: 720, // 默认生成截图框宽度
    autoCropHeight: 180, // 默认生成截图框高度
    fixedBox: false, // 固定截图框大小 不允许改变
    fixed: true, // 是否开启截图框宽高固定比例
    fixedNumber: [1, 1], // 截图框的宽高比例
    full: true, // 是否输出原图比例的截图
    canMoveBox: true, // 截图框能否拖动
    original: false, // 上传图片按照原始比例渲染
    centerBox: true, // 截图框是否被限制在图片里面
    infoTrue: true, // true 为展示真实输出图片宽高 false 展示看到的截图框宽高
    mode: 'contain',
});
onMounted(() => {
    if (props.options) {
        Object.assign(innerOption.value, props.options, { img: props.blob });
    } else {
        Object.assign(innerOption.value, { img: props.blob });
    }
});

const cropperRef = ref();
const finish = () => {
    if (btnLoading.value) return;
    cropperRef.value.getCropBlob((blob: any) => {
        btnLoading.value = true;
        emit('confirm', blob);
    });
};

</script>

<template>
    <div class="oa__editor-img">
        <div class="edit-container">
            <div class="img-wrapper">
                <vueCropper
                    ref="cropperRef"
                    :img="innerOption.img"
                    :output-type="innerOption.outputType"
                    :info="true"
                    :full="innerOption.full"
                    :can-scale="innerOption.canScale"
                    :can-move-box="innerOption.canMoveBox"
                    :original="innerOption.original"
                    :auto-crop="innerOption.autoCrop"
                    :auto-crop-width="innerOption.autoCropWidth"
                    :auto-crop-height="innerOption.autoCropHeight"
                    :fixed="innerOption.fixed"
                    :fixed-number="innerOption.fixedNumber"
                    :center-box="innerOption.centerBox"
                    :info-true="innerOption.infoTrue"
                    :mode="innerOption.mode"
                    :fixed-box="innerOption.fixedBox"
                />
            </div>
        </div>
        <div class="btns">
            <el-button
                :width="80"
                type="primary"
                :loading="btnLoading"
                @click="finish"
            >
                确认
            </el-button>
            <el-button :width="80" type="default" @click="emit('cancel')">取消</el-button>
        </div>
    </div>
</template>

<style lang="scss">
.oa__editor-img {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    justify-content: center;

    .edit-container {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: rgba(0, 0, 0, .8);

        .img-wrapper {
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .vue-cropper {
            background-image: none;
        }
    }

    .btns {
        height: 64px;
        flex-shrink: 0;
        background-color: #fff;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
    }
}
</style>