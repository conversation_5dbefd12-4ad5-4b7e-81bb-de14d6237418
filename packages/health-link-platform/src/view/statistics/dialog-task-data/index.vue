<template>
    <el-dialog
        v-model="isShowDialogTaskData"
        :title="title"
        width="860px"
        class="dialog-task-data"
    >
        <el-scrollbar class="list-wrapper">
            <canvas ref="chartRef"></canvas>
        </el-scrollbar>
        <template #footer>
            <span class="dialog-footer">
                <el-button
                    @click="isShowDialogTaskData = false"
                >
                    关闭
                </el-button>
            </span>
        </template>

        <dialog-detail-data
            v-if="dialogDetailDataMode.visible.value"
            v-model="dialogDetailDataMode.visible.value"
            :info="toolsParams.info"
        />
    </el-dialog>
</template>

<script lang="ts" setup>
import Chart from 'chart.js/auto';
import { ref, computed, onMounted, nextTick } from 'vue';
import { createTaskDataController } from './controller';
import * as utils from '@/common/utils';

import DialogDetailData from '../dialog-detail-data/index.vue';

const chartRef = ref(null);

const props = defineProps({
    modelValue: {
        type: Number,
        default: 0,
    },
    dateRange: {
        type: Array,
        default: () => [],
    },
    taskData: {
        type: Object,
        required: true,
    },
});

const $emit = defineEmits([
    'update:modelValue', // 更新显示状态
    'select', // 选择
]);

const isShowDialogTaskData = computed({
    get() {
        return !!props.modelValue;
    },
    set(val:boolean) {
        $emit('update:modelValue', +val);
    },
});

const {
    dialogDetailDataMode,
    toolsParams,
} = createTaskDataController();

// 标题
const title = computed(() => `任务数据（${props.taskData?.displayName || ''}）`);

onMounted(async () => {
    await nextTick();
    createChartView();
});

/**
 * 创建图表数据
 * <AUTHOR>
 * @date 2025-07-29
 * @param {Array} dataList
 * @returns {Object}
 */
const createChartData = (dataList: any) => {
    const chartData = {
        labels: [] as string[],
        datasets: [
            {
                label: '总任务数',
                backgroundColor: 'rgb(144, 147, 153, 0.5)',
                data: [] as number[],
                stack: 'checkingTotal',
            },
            {
                label: '上报成功',
                backgroundColor: 'rgb(75, 192, 192, 0.5)',
                data: [] as number[],
                stack: 'checkingSuccess',
            },
            {
                label: '上报失败',
                backgroundColor: 'rgb(255, 159, 64, 0.5)',
                data: [] as number[],
                stack: 'checkingFail',
            },
            {
                label: '待上报',
                backgroundColor: 'rgb(54, 162, 235, 0.5)',
                data: [] as number[],
                stack: 'checkingWill',
            },
        ],
    };
    dataList.forEach((item: any) => {
        chartData.labels.push(item.displayName);

        const totalDataCount = item.totalDataCount || 0;
        const uploadSuccess = item.uploadSuccess || 0;
        const uploadFailed = item.uploadFailed || 0;
        const uploadPending = utils.red(totalDataCount, uploadSuccess, uploadFailed);

        chartData.datasets[0].data.push(totalDataCount);
        chartData.datasets[1].data.push(uploadSuccess);
        chartData.datasets[2].data.push(uploadFailed);
        chartData.datasets[3].data.push(uploadPending);
    });
    return chartData;
};

/**
 * 创建图表
 * <AUTHOR>
 * @date 2025-07-29
 * @param {Array} regionList
 * @param {Array} dataList
 */
const createChartView = () => {
    if (!chartRef.value) {
        return;
    }
    if (toolsParams.myChart) {
        toolsParams.myChart.destroy();
    }
    const dataList = props.taskData?.tasks || [];
    const chartData = createChartData(dataList);

    toolsParams.myChart = new Chart(chartRef.value, {
        type: 'bar',
        data: chartData,
        options: {
            indexAxis: 'y',
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                tooltip: {
                    enabled: true,
                    callbacks: {
                        label(context) {
                            const dataset = context.dataset;
                            const datasetIndex = context.datasetIndex;
                            const list = [
                                `${dataset.label}: ${context.raw} 笔`,
                            ];
                            if (datasetIndex !== 0) {
                                const dataIndex = context.dataIndex;
                                const total = chartData.datasets[0].data[dataIndex];
                                list.push(`占比: ${(context.raw / total * 100).toFixed(2)} %`);
                            }
                            return list.join('，');
                        },
                    },
                },
            },
            onClick: (_, elements) => {
                if (
                    toolsParams.myChart
                    && elements.length > 0
                ) {
                    const index = elements[0].index;
                    const datasetIndex = elements[0].datasetIndex;
                    onClickShowDetail(index, datasetIndex);
                }
            },
        },
    });
};

/**
 * 当点击显示详情数据
 * <AUTHOR>
 * @date 2025-10-30
 * @param {any} region:any
 * @param {any} datasetIndex:number
 * @returns {any}
 */
const onClickShowDetail = (index: number, datasetIndex: number) => {
    const target = props.taskData?.tasks[index] || null;
    if (!target) {
        return;
    }
    const info = {
        startTime: props.dateRange[0], // 开始日期
        endTime: props.dateRange[1], // 结束日期
        taskId: target.taskId || '', // 任务ID
        uploadStatus: (() => {
            if (datasetIndex === 1) {
                // 上报成功
                return 1;
            }
            if (datasetIndex === 2) {
                // 上报失败
                return 2;
            }
            if (datasetIndex === 3) {
                // 待上报
                return 0;
            }
            return '';
        })(),
    };
    toolsParams.info = info;
    dialogDetailDataMode.show();
};
</script>

<style lang="scss">
    .dialog-task-data {
        > .el-dialog__body {
            height: calc(100vh - 200px);
            padding: 0;
        }

        .list-wrapper {
            padding-right: 10px;

            canvas {
                height: calc(6 * 120px) !important;
            }
        }
    }
</style>