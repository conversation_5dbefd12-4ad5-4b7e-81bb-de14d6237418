<script setup lang="ts">
import { <PERSON> } from '@/vendor/jenkins';
import { computed, onMounted, reactive } from 'vue';
import { DeploymentApi } from '@/api/deployment-api';
import CreateDeployJenkinsInfoDto = AbcAPI.CreateDeployJenkinsInfoDto;

const props = defineProps({
    id: {
        type: String,
        required: true,
    },
});
const vmData = reactive({
    loading: false,
    businessList: [
        {
            label: 'abc-global',
            value: '/job/abc-global/',
            supportRegion: 0,
        },
        {
            label: 'abc-oa',
            value: '/job/abc-oa/',
            supportRegion: 0,
        },
        {
            label: 'abc-bis',
            value: '/job/abc-bis/',
            supportRegion: 0,
        },
        {
            label: 'abc-his',
            value: '/job/abc-his/',
            supportRegion: 1,
        },
        {
            label: 'mira',
            value: '/job/mira/',
            supportRegion: 0,
        },
    ],
    selectedBusiness: 'abc-his',
    envList: [
        {
            label: 'dev',
            value: '/job/k8s-dev/',
        },
        {
            label: 'test',
            value: '/job/k8s-test/',
        },
        {
            label: 'pre',
            value: '/job/pre-release/',
        },
        {
            label: 'gray',
            value: '/job/k8s-gray/',
        },
        {
            label: 'prod',
            value: '/job/k8s-prod/',
        },
    ],
    selectedEnv: 'pre',
    selectedPipeline: '',
    jobList: <Array<any>>[],
});

const filteredJobList = computed(() => vmData.jobList.filter(item => !vmData.selectedPipeline || item.pipeline === vmData.selectedPipeline));

const pipelineOptions = computed(() => [...new Set(vmData.jobList.map(job => job.pipeline))]);

const businessJenkinsUrl = computed(
    () => (
        vmData.businessList.find(item => item.label === vmData.selectedBusiness)?.value || ''
    ) + 'job/' + vmData.selectedEnv + '/',
);
const jenkins = new Jenkins();

onMounted(() => {
});

async function handleSyncClick() {
    vmData.loading = true;
    const [
        jenkinsResponse,
        deployJenkinsResponse,
    ] = await Promise.all([
        jenkins.getJobsWithLastBuild(jenkins.decodeQueryPath(businessJenkinsUrl.value)),
        DeploymentApi.getApiLowCodeDeploymentDeployJenkinsInfo(vmData.selectedEnv),
    ]);
    if (jenkinsResponse) {
        let deployJenkinsMap = new Map();
        deployJenkinsResponse.rows.forEach((deployJenkinsInfo: any) => {
            deployJenkinsMap.set(deployJenkinsInfo.name, deployJenkinsInfo);
        });
        const jobList = jenkinsResponse.jobs.map((item: any) => {
            let paramDef = item.property?.find((prop: any) => prop._class === 'hudson.model.ParametersDefinitionProperty');
            let property = paramDef?.parameterDefinitions.reduce((acc: any, cur: any) => {
                acc[cur.name] = cur.defaultParameterValue?.value || '';
                return acc;
            }, {});
            let latestTag = item.lastBuild?.actions?.find(
                (item: any) => item._class === 'hudson.model.ParametersAction',
            )?.parameters.find(
                (item: any) => item.name === 'repoTag',
            )?.value || '';
            return {
                name: item.name,
                url: item.url,
                ...property,
                latestTag,
                prefix: latestTag.match(/(.*)-[ftpvg]2.*/)?.[1] || deployJenkinsMap.get(item.name)?.tagPrefix,
            };
        });
        vmData.jobList = createPostData(jobList);
    }
    vmData.loading = false;
}

function isJobSupportOnlyDeploy(job: any) {
    const unSupportedPipelines = [
        'sc-jenkinsfile.static.groovy', // 前端工程，需要分离部署的方案
        'sc-jenkinsfile.static.node14.groovy', // 前端工程，需要分离部署的方案
        'sc-private-jenkinsfile.groovy',
        'sc-private-jenkinsfile.nodejs.groovy',
        'sc-jenkinsfile.weapp.groovy',
        'sc-jenkinsfile.static.only-compile.groovy',
        'sc-jenkinsfile.static.private.groovy',
        'sc-jenkinsfile.abcyunapp.groovy',
        'sc-jenkinsfile.micro-frontend.static.groovy',
        'sc-jenkinsfile.micro-frontend.static.node18.groovy',
        'sc-jenkinsfile.offlinetask.groovy',
        'sc-jenkinsfile.socialalone.groovy',
        'sc-jenkinsfile.static.only-compile.groovy',
    ];
    // 支持独立部署的服务
    const supportedService = [
        'abc-cis-static-login-service',
        'abc-cis-static-pc-service',
        'abc-oa-static-web-service',
        'abc-cis-static-self-service-terminal-service',
        'abc-cis-static-mb-service',
        'abc-bis-static-vc-service',
        'abc-bis-static-vc-admin-service',
        'bp-static-crm-web-service',
        'abc-cis-static-call-number-service',
    ];

    // 支持独立部署的前端 sdk：print，social
    const supportedSdk = [
        'static-print',
        'socialPcModule',
        'AbcLis', // sc-jenkinsfile.static.only-compile.groovy
        'AbcPublicHealth', // sc-jenkinsfile.static.only-compile.groovy
        'AbcyunApp', // sc-jenkinsfile.abcyunapp.groovy
        'static-abc-emr-editor-sdk',
        'static-abc-fe-engine',
        'static-medical-imaging-viewer-sdk',
        'static-mf-deepseek',
        'static-mf-order-cloud',
    ];
    return job.needK8s && (!unSupportedPipelines.includes(job.pipeline) || supportedService.includes(job.serviceName)) || supportedSdk.includes(job.name);
}

// 是否支持分区部署
function isJobSupportRegionDeploy(job: any) {
    const supportRegionDeployPipelines = [
        'sc-jenkinsfile.nodejs.groovy',
        'sc-jenkinsfile.groovy',
        'sc-jenkinsfile.python.groovy',
        'sc-jenkinsfile.config-server.groovy',
        'sc-jenkinsfile.static.node14.groovy',
        'sc-jenkinsfile.static.node16.groovy',
        'sc-jenkinsfile.static-mp.groovy',
        'sc-jenkinsfile.static-scrm.groovy',
        'sc-multipleServices-jenkinsfile.groovy',
        'sc-jenkinsfile.python-tongue.groovy',
        'sc-jenkinsfile.abcyunapp.groovy',
        // 技术支持目录下两个工程在用，要确认是否需要分区部署
        // "sc-jenkinsfile.qa-python.groovy",
        'sc-stat-jenkinsfile.groovy',
        // featureTest
        'Jenkins-npm',
    ];
    // im-sdk 不支持分区
    const unspportedSdk = [
        'imsdk',
    ];

    // 支持分区的前端 sdk
    const supportedSdk = [
        'static-print',
        'socialPcModule',
        'AbcLis', // sc-jenkinsfile.static.only-compile.groovy
        'AbcPublicHealth', // sc-jenkinsfile.static.only-compile.groovy
        'AbcyunApp', // sc-jenkinsfile.abcyunapp.groovy
        'static-abc-emr-editor-sdk',
        'static-abc-fe-engine',
        'static-medical-imaging-viewer-sdk',
        'static-mf-deepseek',
        'static-mf-order-cloud',
    ];
    // 小程序没有 k8s，但也支持分区部署
    return job.needK8s && supportRegionDeployPipelines.includes(job.pipeline) && !unspportedSdk.includes(job.name)
      || job.pipeline === 'sc-jenkinsfile.weapp.groovy'
      || supportedSdk.includes(job.name);
}

async function handleConfigClick() {
    vmData.jobList.forEach(async (job: any) => {
        const configInfo = await jenkins.getJobConfigInfo(job.url);
        try {
            const parser = new DOMParser().parseFromString(configInfo, 'text/xml');
            // 查询 script
            const scriptPath = parser.getElementsByTagName('scriptPath')?.[0];
            if (scriptPath) {
                job.pipeline = scriptPath.firstChild?.nodeValue;
            } else {
                // 解析是否是自定义的 script
                const script = parser.getElementsByTagName('script')?.[0];
                if (script) {
                    job.pipeline = script.firstChild?.nodeValue;
                }
            }

            // 查询 execType
            const choiceParams = parser.getElementsByTagName('hudson.model.ChoiceParameterDefinition');
            if (choiceParams?.length) {
                for (let i = 0; i < choiceParams.length; i++) {
                    let choiceParam = choiceParams[i];
                    if (choiceParam.getElementsByTagName('name')?.[0]?.firstChild?.nodeValue === 'execType') {
                        job.supportOnlyDeploy = +isJobSupportOnlyDeploy(job);
                        break;
                    }
                }
            }

            // 是否支持分区部署
            if (vmData.selectedBusiness === 'abc-his') {
                job.supportRegionDeploy = +isJobSupportRegionDeploy(job);
            }
        } catch (e) {

        }
    });
}

async function handleSubmitClick() {
    vmData.loading = true;
    await DeploymentApi.postApiLowCodeDeploymentDeployJenkinsInfoBatchCreate({
        data: vmData.jobList.map(item => {
            const { pipeline, ...rest } = item;
            return {
                ...rest,
            };
        }),
        env: vmData.selectedEnv,
        business: vmData.selectedBusiness,
    });
    vmData.loading = false;
}

function createPostData(jobList: any[]): Array<CreateDeployJenkinsInfoDto> {
    return jobList.map(job => ({
        name: job.name,
        serviceName: job.name === 'PcFeatureTest' ? 'pc-feature-test' : job.dockerName,
        needK8s: job.name !== 'imsdk' && job.dockerName ? 1 : 0,
        supportOnlyDeploy: 0,
        supportRegionDeploy: 0,
        business: vmData.selectedBusiness,
        env: job.buildEnv || vmData.selectedEnv, // 优先取 buildEnv，再使用当前选择的，兼容 featureTest
        url: job.url,
        tagPrefix: job.prefix,
        latestTag: job.latestTag,
    }));
}

</script>
<template>
    <el-row>
        <el-col>
            <el-select v-model="vmData.selectedBusiness" size="small">
                <el-option
                    v-for="env in vmData.businessList"
                    :key="env.label"
                    :label="env.label"
                    :value="env.label"
                >
                </el-option>
            </el-select>
            <el-select v-model="vmData.selectedEnv" size="small">
                <el-option
                    v-for="env in vmData.envList"
                    :key="env.label"
                    :label="env.label"
                    :value="env.label"
                >
                </el-option>
            </el-select>
            <el-select v-model="vmData.selectedPipeline" size="small">
                <el-option
                    v-for="env in pipelineOptions"
                    :key="env"
                    :label="env"
                    :value="env"
                >
                </el-option>
            </el-select>
            <el-button
                size="small"
                type="primary"
                style="margin-left: 16px;"
                @click="handleSyncClick"
            >
                获取最新
            </el-button>

            <el-button
                size="small"
                type="primary"
                style="margin-left: 16px;"
                @click="handleConfigClick"
            >
                获取config
            </el-button>

            <el-button
                size="small"
                type="success"
                style="margin-left: 16px;"
                @click="handleSubmitClick"
            >
                提交
            </el-button>
        </el-col>
    </el-row>
    <el-row>
        <el-table v-loading="vmData.loading" :data="filteredJobList" style="margin-top: 12px;">
            <el-table-column prop="name" label="任务名"></el-table-column>
            <el-table-column prop="url" label="url"></el-table-column>
            <el-table-column prop="env" label="env" width="60"></el-table-column>
            <el-table-column prop="tagPrefix" label="tag前缀">
                <template #default="{row}">
                    <el-input v-model="row.tagPrefix"></el-input>
                </template>
            </el-table-column>
            <el-table-column prop="latestTag" label="最近tag">
            </el-table-column>
            <el-table-column prop="serviceName" label="服务名"></el-table-column>
            <el-table-column prop="pipeline" label="pipeline" min-width="120"></el-table-column>
            <el-table-column prop="needK8s" label="K8s部署" width="80">
                <template #default="{row}">
                    <el-checkbox v-model="row.needK8s" :true-label="1" :false-label="0"></el-checkbox>
                </template>
            </el-table-column>
            <el-table-column prop="supportOnlyDeploy" label="独立部署" width="80">
                <template #default="{row}">
                    <el-checkbox v-model="row.supportOnlyDeploy" :true-label="1" :false-label="0"></el-checkbox>
                </template>
            </el-table-column>
            <el-table-column prop="supportRegionDeploy" label="分区部署" width="80">
                <template #default="{row}">
                    <el-checkbox v-model="row.supportRegionDeploy" :true-label="1" :false-label="0"></el-checkbox>
                </template>
            </el-table-column>
        </el-table>
    </el-row>
</template>

<style lang="scss">

</style>
