<script setup lang="ts">
import { computed, inject, PropType } from 'vue';

const props = defineProps({
    label: {
        type: String,
    },
    value: {
        type: String,
    },
    labelWidth: {
        type: String,
        default: '',
    },
    labelAlign: {
        type: String as PropType<'left'|'right'|'center'>,
        default: 'left',
    },
    valueAlign: {
        type: String as PropType<'left'|'right'|'center'>,
        default: 'left',
    },
    verticalAlign: {
        type: String as PropType<'top'|'bottom'|'center'>,
        default: 'center',
    },
    size: {
        type: String as PropType<'large'|'normal'|'small'>,
        default: 'normal',
    },
    height: {
        type: String,
        default: '',
    },
});

const parentLabelWidth = inject<string>('label-width');
const parentSize = inject<string>('size');
const parentVerticalAlign = inject<string>('verticalAlign');
const parentCellHeight = inject<string>('cellHeight');

const computedLabelWidth = computed(() => parentLabelWidth || props.labelWidth);
const computedCellHeight = computed(() => parentCellHeight || props.height || '48px');
const computedSize = computed(() => parentSize || props.size);
const computedSizeClass = computed(() => `oa-cell-wrapper--${computedSize.value}`);
const computedAlignClass = computed(() => `oa-cell-wrapper--${parentVerticalAlign || props.verticalAlign}`);

</script>
<template>
    <div :class="['oa-cell-wrapper', computedSizeClass, computedAlignClass]" :style="{height: computedCellHeight, minHeight: computedCellHeight}">
        <div class="oa-cell__label-wrapper" :style="{minWidth: computedLabelWidth}">
            <slot name="label">{{ label }}</slot>
        </div>
        <div class="oa-cell__value-wrapper">
            <slot>{{ value }}</slot>
        </div>
    </div>
</template>

<style lang="scss">
.oa-cell-wrapper {
    display: flex;
    min-height: 48px;
    align-items: center;

    &--top {
        align-items: flex-start;
    }

    &--large {
        min-height: 64px;
    }

    &--small {
        min-height: 32px;
    }

    .oa-cell__label-wrapper {
        color: var(--oa-text-color-2);
    }

    .oa-cell__value-wrapper {
        margin-left: 8px;
        color: var(--oa-text-color);
    }
}

</style>
