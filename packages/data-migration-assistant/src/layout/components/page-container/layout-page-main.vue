<template>
    <div class="layout-page-main">
        <div v-if="$slots.header" class="layout-page-main__header">
            <slot name="header"></slot>
        </div>
        <div class="layout-page-main__content">
            <slot></slot>
        </div>
    </div>
</template>

<style lang="scss">
@use "@/style/mixins/mixins.scss" as mixins;

.layout-page-main {
    height: 100%;
    background: var(--oa-gray-2);

    @include mixins.flexLayout(flex-start, flex-start, column);

    &__header {
        width: 100%;
        background: var(--oa-white);
        border-bottom: var(--oa-pharmacy-border);
    }

    &__content {
        flex: 1;
        width: 100%;
        overflow-x: hidden;
        overflow-y: auto;
    }
}
</style>
