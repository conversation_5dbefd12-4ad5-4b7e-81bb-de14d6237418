/*
 * <AUTHOR> 
 * @DateTime 2025-09-08 19:40:32 
 */
import BaseApi from './base-api';

export default class StatisticsApi extends BaseApi {
    /**
     * 获取统计数据
     * <AUTHOR>
     * @date 2025-09-08
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    static fetchStatisticsData(params: any) {
        return this.fetchPack({
            url: '/api/management/healthlink/upload-stat',
            method: 'GET',
            params,
        });
    }
    
    /**
     * 获取上传记录数据
     * <AUTHOR>
     * @date 2025-09-08
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    static fetchUploadRecordData(params: any) {
        return this.fetchPack({
            url: '/api/management/healthlink/upload-data',
            method: 'GET',
            params,
        });
    }
}