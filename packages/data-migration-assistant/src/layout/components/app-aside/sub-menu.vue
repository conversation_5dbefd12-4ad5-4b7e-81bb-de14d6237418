<script setup lang="ts">

import { MenuItemClicked } from 'element-plus';
import { useRouter } from 'vue-router';
import { useThemeConfigStore } from '@/store/theme-config';
import { computed } from 'vue';

const router = useRouter();
const themeConfig = useThemeConfigStore();

const props = defineProps({
    menu: {
        type: Object,
        required: true,
    },
});

const subMenus = computed(() => (props.menu.children || []).filter((item: any) => !item.meta?.hidden));

function handleMenuClick(menuItem: MenuItemClicked) {
    if (themeConfig.isMobile) {
        themeConfig.setCollapse(true);
    }
    router.push({
        name: menuItem.index,
    });
}

</script>
<template>
    <el-sub-menu v-if="subMenus.length" :index="menu.name" style="margin-top: 8px;">
        <template #title>
            <el-icon v-if="menu.meta?.icon"><component :is="menu.meta.icon"></component></el-icon>
            <span>{{ menu.meta.name }}</span>
        </template>
        <sub-menu v-for="subMenu in menu.children" :menu="subMenu"></sub-menu>
    </el-sub-menu>
    <el-menu-item
        v-else-if="!menu.meta?.hidden"
        :index="menu.name"
        @click="handleMenuClick"
    >
        <template v-if="menu.meta?.icon">
            <oa-icon v-if="menu.meta.icon.includes(':')" :icon="menu.meta.icon"></oa-icon>
            <el-icon v-else><component :is="menu.meta.icon"></component></el-icon>
        </template>
        <span>{{ menu.meta?.name }}</span>
    </el-menu-item>
</template>

<style scoped>

</style>
