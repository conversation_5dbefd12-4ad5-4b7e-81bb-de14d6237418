import { mergeAttributes, Node } from '@tiptap/core';

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        videoWrapper: {
            /**
             * Add an videoWrapper
             */
            setVideoWrapper: (options: { class?: string, width?: string, style?: string }) => ReturnType,
        }
    }
}
export interface VideoWrapperOptions {
    HTMLAttributes: Record<string, any>,
}
export const CustomVideoWrapperExtension = Node.create<VideoWrapperOptions>({
    name: 'videoWrapper',

    addAttributes() {
        return {
            width: {
                default: null,
            },
            class: {
                default: null,
            },
            videos: {
                default: null,
            },
            style: {
                default: null,
            },
        };
    },

    addOptions() {
        return {
            HTMLAttributes: {},
        };
    },

    content() {
        return 'video*';
    },

    group: 'block',

    parseHTML(): any {
        return [
            {
                tag: 'div',
                getAttrs: (dom: HTMLElement) => dom.className.includes('abc-editor__custom-video-wrapper'),
            },
        ];
    },

    renderHTML({ HTMLAttributes, node }): any {
        const justifyContentClass = node.childCount > 1 ? 'flex-around' : 'flex-center';
        // 用正则替换HTMLAttributes.class中的flex-around以及flex-center为justifyContentClass,如果两个都不存在则添加justifyContentClass
        if (HTMLAttributes.class?.includes('flex-around') || HTMLAttributes.class?.includes('flex-center')) {
            HTMLAttributes.class = HTMLAttributes.class?.replace(/flex-around|flex-center/g, justifyContentClass);
        } else {
            HTMLAttributes.class = `${HTMLAttributes.class} ${justifyContentClass}`;
        }
        if (!HTMLAttributes.class?.includes('abc-editor__custom-video-wrapper')) {
            HTMLAttributes.class = `${HTMLAttributes.class} abc-editor__custom-video-wrapper`;
        }
        HTMLAttributes.class += ` data-${Math.random()}`;

        const wrapperAttributes = mergeAttributes(
            HTMLAttributes,
            this.options.HTMLAttributes,
        );
        return ['div', wrapperAttributes, 0];
    },

    addCommands() {
        return {
            setVideoWrapper: options => ({ commands }) => commands.insertContent({
                type: this.name,
                attrs: options,
            }),
        };
    },
});
