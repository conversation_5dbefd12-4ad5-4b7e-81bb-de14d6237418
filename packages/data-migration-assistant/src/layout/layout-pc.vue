<script setup lang="ts">
import Aside from './components/app-aside/aside.vue';
import Header from './components/header.vue';
import Main from './components/main.vue';
</script>
<template>
    <el-config-provider>
        <el-container class="layout-pc-container">
            <Aside></Aside>
            <el-container>
                <el-header>
                    <Header></Header>
                </el-header>
                <el-main class="layout-pc__content">
                    <Main></Main>
                </el-main>
            </el-container>
        </el-container>
    </el-config-provider>
</template>
<style lang="scss">
    .el-main.layout-pc__content {
        padding: 0 8px 8px 0;
    }
</style>
