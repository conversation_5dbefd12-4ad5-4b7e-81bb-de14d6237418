/* 第三方图标字体间距/大小设置
------------------------------- */
@mixin generalIcon {
    font-size: 14px !important;
    display: inline-block;
    vertical-align: middle;
    margin-right: 5px;
    width: 24px;
    text-align: center;
}

/* 文本不换行
------------------------------- */
@mixin text-no-wrap() {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

/* 多行文本溢出
  ------------------------------- */
@mixin text-ellipsis($line: 2) {
    overflow: hidden;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: $line;
    -webkit-box-orient: vertical;
}

// $resident 常驻滚动条
@mixin scrollBar($resident: false, $width: 10px) {
    &::-webkit-scrollbar {
        width: $width;
        height: 12px; // 横向滚动条的宽度是height
    }

    &::-webkit-scrollbar-thumb { /* 滚动条里面小方块 */
        cursor: pointer;
        background: #e6eaed;
        background-clip: content-box;
        border: 1px solid transparent;
        border-radius: 6px;
    }

    &::-webkit-scrollbar-thumb:hover { /* 滚动条里面小方块 */
        cursor: pointer;
        background: #dee2e6;
        background-clip: content-box;
        border: 1px solid #dee2e6;
        border-radius: 6px;
    }

    @if $resident {
        &::-webkit-scrollbar-thumb { /* 滚动条里面小方块 */
            visibility: visible;
        }
    }

    @else {
        &::-webkit-scrollbar-thumb { /* 滚动条里面小方块 */
            visibility: hidden;
        }

        &:hover::-webkit-scrollbar-thumb {
            visibility: visible;
        }
    }

    &::-webkit-scrollbar-track { /* 滚动条里面轨道 */
        background: transparent;
        opacity: 0;
    }

    &:hover::-webkit-scrollbar-track { /* 滚动条里面轨道 */
        background: transparent;
        opacity: 0;
    }
}

/* flex布局配置
------------------------------- */
@mixin flexLayout($justify: center, $align: center, $direction: row, $wrap: nowrap) {
    display: flex;
    flex-direction: $direction;
    flex-wrap: $wrap;
    align-items: $align;
    justify-content: $justify;
}

@mixin flex-direction( $direction: row,) {
    display: flex;
    flex-direction: $direction;
}
