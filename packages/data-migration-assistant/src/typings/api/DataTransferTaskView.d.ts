declare namespace  AbcAPI {
    
    type DataTransferTaskView = {    
        //门店id
        clinicId:string    
        //竞品信息
        competitorInfo?:DataTransferAutoCompetitorSupportView    
        //创建时间
        created:string    
        //创建人
        createdBy:number    
        //创建人名
        createdByName:string    
        //数据量
        dataCount:number    
        //数据类型
        dataType:string    
        //0/null:成功  1:Jenkins异常中断 2:解析失败 3:导入失败 4:导出失败
        errCode:number    
        //错误详情
        errDetail:string    
        //主键ID
        id:string    
        //修改人名
        lastModifiedByName:string    
        //状态 0:未开始 10:解析中 20:处理中 30:完成 90:取消 99:失败
        status:number    
        //任务名称
        taskName:string    
        //类型 1:药品建档导入 2:药品入库导入
        type:number    
    }
}
