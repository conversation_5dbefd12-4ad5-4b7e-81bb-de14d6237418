/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import { reactive } from 'vue';
import createDialogModel from '@/model/dialog';

export const createTaskDataController = () => {
    const dialogDetailDataMode = createDialogModel();

    // 工具栏参数
    const toolsParams = reactive({
        myChart: null as any,
        info: null as any,
    });

    return {
        dialogDetailDataMode,
        toolsParams,
    };
};