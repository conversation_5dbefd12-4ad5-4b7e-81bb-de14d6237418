declare namespace  AbcAPI {
    
    type FindClinicInfoListItemVo = {    
        //门店连锁ID
        chainId:string    
        //门店连锁名称
        chainName:string    
        //门店ID
        clinicId:string    
        //门店名称
        clinicName:string    
        //诊所类型
        hisType:number    
        //门店类型
        nodeTypeName:string    
        //诊所版本
        editionId:string    
        //用户ID
        employeeId:string    
        //用户
        employeeName:string    
        //是否关注微信
        employeeSubscribe:number    
        //微信昵称
        employeeWechatName:string    
        //openId
        employeeOpenId:string    
        //电话
        employeeMobile:string    
        //门店分区
        regionId:string    
        //是否为管理员
        roleId:number    
        //门店过滤类型：0: 不过滤，1: 连锁模式的总部和子店，2: 单店模式的总部和子店，4: 所有总部，5: 连锁总部，6: 单店总部，8: 全部子店，9: 连锁子店，10: 单店子店， 11:非单店总部
        nodeTypeFilter:number    
        //是否为试用 0: 非试用，1: 试用
        isTrial:number    
    }
}
