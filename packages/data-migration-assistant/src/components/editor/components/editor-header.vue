<script setup lang="ts">
import 'remixicon/fonts/remixicon.css';
import { reactive, ref } from 'vue';

const props = defineProps({
    editor: {
        type: Object,
        default: () => {},
    },
});
const fontColor = ref('#000000');
const fontBackgroundColor = ref('#000000');
const predefineColors = ref([
    '#000000',
    '#ffffff',
    '#929393',
    '#2d70ef',
    '#ff4500',
    '#ff8c00',
    '#ffd700',
    '#90ee90',
    '#00ced1',
    '#1e90ff',
    '#c71585',
    'rgba(255, 69, 0, 0.68)',
    'rgb(255, 120, 0)',
    'hsv(51, 100, 98)',
    'hsva(120, 40, 94, 0.5)',
    'hsl(181, 100%, 37%)',
    'hsla(209, 100%, 56%, 0.73)',
    '#c7158577',
]);
/**
 * @description: 字体颜色变化
 * @date: 2023-11-30 14:26:17
 * @author: Horace
 * @param color: 变化的颜色
 * @return
*/
const handleColorChange = (color: string) => {
    props.editor.chain().focus().setColor(color).run();
};
/**
 * @description: 字体背景颜色变化
 * @date: 2023-11-30 14:26:32
 * @author: Horace
 * @param color: 变化的颜色
 * @return
*/
const handleBackColorChange = (color: string) => {
    props.editor.chain().focus().toggleHighlight({ color }).run();
};
const currentFontFamily = ref();
const fontFamilyOptions = reactive<any[]>([
    {
        label: 'Microsoft YaHei',
        value: 'Microsoft YaHei',
    },
    {
        label: 'Comic Sans',
        value: 'Comic Sans MS, Comic Sans',
    },
    { label: 'Inter', value: 'Inter' },
    { label: 'serif', value: 'serif' },
    { label: 'monospace', value: 'monospace' },
    { label: 'cursive', value: 'cursive' },
]);
/** 字体变化
 * @description:
 * @date: 2023-11-30 16:39:48
 * @author: Horace
 * @param fontFamily: 变化的字体
 * @return
*/
const handleFontFamilyChange = (fontFamily: string) => {
    currentFontFamily.value = fontFamily;
    props.editor.chain().focus().setFontFamily(fontFamily).run();
};
/**
 * @description: 设置超链接
 * @date: 2023-11-30 16:38:25
 * @author: Horace
 * @param null:
 * @return
*/
const setLink = () => {
    const previousUrl = props.editor.getAttributes('link').href;
    const url = window.prompt('URL', previousUrl);
    if (url === null) {
        return;
    }
    if (url === '') {
        props.editor
                        .chain()
                        .focus()
                        .extendMarkRange('link')
                        .unsetLink()
                        .run();
        return;
    }
    // update link
    props.editor
                    .chain()
                    .focus()
                    .extendMarkRange('link')
                    .setLink({ href: url })
                    .run();
};
const setImage = () => {
    const url = window.prompt('URL');
    if (url) {
        const newNode = props.editor.view.state.schema.nodes.image.create({
            src: url,
            alt: url,
        });

        const wrapperNode = props.editor.view.state.schema.nodes.imageWrapper.create(
            {},
            [newNode],
        );
        const { tr } = props.editor.view.state;
        props.editor.view.dispatch(tr.replaceSelectionWith(wrapperNode));
    }
};
const setVideo = () => {
    const url = window.prompt('URL');
    if (url) {
        const newNode = props.editor.view.state.schema.nodes.video.create({
            src: url,
        });

        const wrapperNode = props.editor.view.state.schema.nodes.videoWrapper.create(
            {},
            [newNode],
        );
        const { tr } = props.editor.view.state;
        props.editor.view.dispatch(tr.replaceSelectionWith(wrapperNode));
    }
};
</script>
<template>
    <div class="abc-editor-header">
        <el-tooltip content="粗体">
            <div
                class="abc-editor-header__menu-item flex-center"
                :disabled="!editor.can().chain().focus().toggleBold().run()"
                :class="{ 'is-active': editor.isActive('bold') }"
                @click="editor.chain().focus().toggleBold().run()"
            >
                <i class="menu-item__icon ri-bold"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="斜体">
            <div
                class="abc-editor-header__menu-item flex-center"
                :disabled="!editor.can().chain().focus().toggleItalic().run()"
                :class="{ 'is-active': editor.isActive('italic') }"
                @click="editor.chain().focus().toggleItalic().run()"
            >
                <i class="menu-item__icon ri-italic"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="下划线">
            <div
                class="abc-editor-header__menu-item flex-center"
                :class="{ 'is-active': editor.isActive('underline')}"
                @click="editor.chain().focus().toggleUnderline().run()"
            >
                <i class="menu-item__icon ri-underline"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="删除线">
            <div
                class="abc-editor-header__menu-item flex-center"
                :disabled="!editor.can().chain().focus().toggleStrike().run()"
                :class="{ 'is-active': editor.isActive('strike') }"
                @click="editor.chain().focus().toggleStrike().run()"
            >
                <i class="menu-item__icon ri-strikethrough"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="代码">
            <div
                class="abc-editor-header__menu-item flex-center"
                :disabled="!editor.can().chain().focus().toggleCode().run()"
                :class="{ 'is-active': editor.isActive('code') }"
                @click="editor.chain().focus().toggleCode().run()"
            >
                <i class="menu-item__icon ri-code-view"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="段落">
            <div
                class="abc-editor-header__menu-item flex-center"
                :class="{ 'is-active': editor.isActive('paragraph') }"
                @click="editor.chain().focus().setParagraph().run()"
            >
                <i class="menu-item__icon ri-paragraph"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="字体">
            <div
                class="abc-editor-header__menu-item flex-center"
                :class="{ 'is-active': editor.isActive('textStyle', { fontFamily: currentFontFamily }) }"
            >
                <el-dropdown trigger="click" @command="handleFontFamilyChange">
                    <span class="el-dropdown-link">
                        <i class="menu-item__icon ri-font-family"></i>
                    </span>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item
                                v-for="fontFamily in fontFamilyOptions"
                                :key="fontFamily.value"
                                :command="fontFamily.value"
                                :style="{fontFamily: fontFamily.value}"
                            >
                                {{ fontFamily.label }}
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
        </el-tooltip>
        <el-tooltip content="文字颜色">
            <div
                class="abc-editor-header__menu-item flex-center color-select-wrapper"
                :class="{ 'is-active': editor.isActive('textStyle', { color: fontColor }) }"
            >
                <i class="menu-item__icon ri-font-color"></i>
                <el-color-picker
                    v-model="fontColor"
                    class="color-select-picker"
                    size="small"
                    show-alpha
                    :predefine="predefineColors"
                    @change="handleColorChange"
                    @active-change="handleColorChange"
                />
            </div>
        </el-tooltip>
        <el-tooltip content="背景颜色">
            <div
                class="abc-editor-header__menu-item flex-center  color-select-wrapper"
                style="background: #2e2e2e;"
                :class="{ 'is-active': editor.isActive('highlight', { color:fontBackgroundColor }) }"
            >
                <i class="menu-item__icon ri-font-family"></i>
                <el-color-picker
                    v-model="fontBackgroundColor"
                    size="small"
                    show-alpha
                    :predefine="predefineColors"
                    @change="handleBackColorChange"
                />
            </div>
        </el-tooltip>
        <el-tooltip content="超链接">
            <div
                class="abc-editor-header__menu-item flex-center"
                :class="{ 'is-active': editor.isActive('link') }"
                @click="setLink"
            >
                <i class="menu-item__icon ri-link"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="清除样式">
            <div
                class="abc-editor-header__menu-item flex-center"
                @click="editor.chain().focus().unsetAllMarks().run()"
            >
                <i class="menu-item__icon ri-format-clear"></i>
            </div>
        </el-tooltip>
        <el-divider direction="vertical" />
        <el-tooltip content="一级标题">
            <div
                class="abc-editor-header__menu-item flex-center"
                :class="{ 'is-active': editor.isActive('heading', { level: 1 }) }"
                @click="editor.chain().focus().toggleHeading({ level: 1 }).run()"
            >
                <i class="menu-item__icon ri-h-1"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="二级标题">
            <div
                class="abc-editor-header__menu-item flex-center"
                :class="{ 'is-active': editor.isActive('heading', { level: 2 }) }"
                @click="editor.chain().focus().toggleHeading({ level: 2 }).run()"
            >
                <i class="menu-item__icon ri-h-2"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="三级标题">
            <div
                class="abc-editor-header__menu-item flex-center"
                :class="{ 'is-active': editor.isActive('heading', { level: 3 }) }"
                @click="editor.chain().focus().toggleHeading({ level: 3 }).run()"
            >
                <i class="menu-item__icon ri-h-3"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="四级标题">
            <div
                class="abc-editor-header__menu-item flex-center"
                :class="{ 'is-active': editor.isActive('heading', { level: 4 }) }"
                @click="editor.chain().focus().toggleHeading({ level: 4 }).run()"
            >
                <i class="menu-item__icon ri-h-4"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="五级标题">
            <div
                class="abc-editor-header__menu-item flex-center"
                :class="{ 'is-active': editor.isActive('heading', { level: 5 }) }"
                @click="editor.chain().focus().toggleHeading({ level: 5 }).run()"
            >
                <i class="menu-item__icon ri-h-5"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="六级标题">
            <div
                class="abc-editor-header__menu-item flex-center"
                :class="{ 'is-active': editor.isActive('heading', { level: 6 }) }"
                @click="editor.chain().focus().toggleHeading({ level: 6 }).run()"
            >
                <i class="menu-item__icon ri-h-6"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="无序列表">
            <div
                class="abc-editor-header__menu-item flex-center"
                :class="{ 'is-active': editor.isActive('bulletList') }"
                @click="editor.chain().focus().toggleBulletList().run()"
            >
                <i class="menu-item__icon ri-list-check"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="有序列表">
            <div
                class="abc-editor-header__menu-item flex-center"
                :class="{ 'is-active': editor.isActive('orderedList') }"
                @click="editor.chain().focus().toggleOrderedList().run()"
            >
                <i class="menu-item__icon ri-list-ordered"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="列表子集">
            <div
                class="abc-editor-header__menu-item flex-center"
                :disabled="!editor.can().sinkListItem('listItem')"
                @click="editor.chain().focus().sinkListItem('listItem').run()"
            >
                <i class="menu-item__icon ri-list-check-2"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="下标">
            <div
                class="abc-editor-header__menu-item flex-center"
                :class="{ 'is-active': editor.isActive('subscript') }"
                @click="editor.chain().focus().toggleSubscript().run()"
            >
                <i class="menu-item__icon ri-subscript"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="上标">
            <div
                class="abc-editor-header__menu-item flex-center"
                :class="{ 'is-active': editor.isActive('superscript') }"
                @click="editor.chain().focus().toggleSuperscript().run()"
            >
                <i class="menu-item__icon ri-superscript"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="代码块">
            <div
                class="abc-editor-header__menu-item flex-center"
                :class="{ 'is-active': editor.isActive('codeBlock') }"
                @click="editor.chain().focus().toggleCodeBlock().run()"
            >
                <i class="menu-item__icon ri-code-box-line"></i>
            </div>
        </el-tooltip>
        <el-divider direction="vertical" />
        <el-tooltip content="引用块">
            <div
                class="abc-editor-header__menu-item flex-center"
                :class="{ 'is-active': editor.isActive('blockquote') }"
                @click="editor.chain().focus().toggleBlockquote().run()"
            >
                <i class="menu-item__icon ri-double-quotes-l"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="分割线">
            <div
                class="abc-editor-header__menu-item flex-center"
                @click="editor.chain().focus().setHorizontalRule().run()"
            >
                <i class="menu-item__icon ri-separator"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="居左">
            <div
                class="abc-editor-header__menu-item flex-center"
                :class="{ 'is-active': editor.isActive({ textAlign: 'left' }) }"
                @click="editor.chain().focus().setTextAlign('left').run()"
            >
                <i class="menu-item__icon ri-align-left"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="居中">
            <div
                class="abc-editor-header__menu-item flex-center"
                :class="{ 'is-active': editor.isActive({ textAlign: 'center' }) }"
                @click="editor.chain().focus().setTextAlign('center').run()"
            >
                <i class="menu-item__icon ri-align-center"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="居右">
            <div
                class="abc-editor-header__menu-item flex-center"
                :class="{ 'is-active': editor.isActive({ textAlign: 'right' }) }"
                @click="editor.chain().focus().setTextAlign('right').run()"
            >
                <i class="menu-item__icon ri-align-right"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="换行">
            <div
                class="abc-editor-header__menu-item flex-center"
                @click="editor.chain().focus().setHardBreak().run()"
            >
                <i class="menu-item__icon ri-text-wrap"></i>
            </div>
        </el-tooltip>
        <el-divider direction="vertical" />
        <el-tooltip content="图片">
            <div
                class="abc-editor-header__menu-item flex-center"
                @click="setImage"
            >
                <i class="menu-item__icon ri-image-line"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="视频">
            <div
                class="abc-editor-header__menu-item flex-center"
                @click="setVideo"
            >
                <i class="menu-item__icon ri-video-line"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="撤销">
            <div
                class="abc-editor-header__menu-item flex-center"
                :disabled="!editor.can().chain().focus().undo().run()"
                @click="editor.chain().focus().undo().run()"
            >
                <i class="menu-item__icon ri-arrow-go-back-line"></i>
            </div>
        </el-tooltip>
        <el-tooltip content="还原">
            <div
                class="abc-editor-header__menu-item flex-center"
                :disabled="!editor.can().chain().focus().redo().run()"
                @click="editor.chain().focus().redo().run()"
            >
                <i class="menu-item__icon ri-arrow-go-forward-line"></i>
            </div>
        </el-tooltip>
    </div>
</template>
<style lang="scss" scoped>
.abc-editor-header {
    align-items: center;
    background: #0d0d0d;
    border-bottom: 3px solid #0d0d0d;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    display: flex;
    flex: 0 0 auto;
    flex-wrap: wrap;
    padding: 4px;

    &__menu-item {
        background: transparent;
        border: none;
        border-radius: 4px;
        color: #fff;
        cursor: pointer;
        height: 28px;
        line-height: 28px;
        padding: 4px;
        margin-right: 4px;
        width: 28px;

        .el-dropdown-link {
            color: #fff;
        }

        &.color-select-wrapper {
            position: relative;

            :deep .el-color-picker {
                position: absolute;
                top: 0;
                left: 0;
                opacity: 0;
            }
        }

        &.is-active,
        &:hover {
            background-color: #303030;
        }
    }
}
</style>