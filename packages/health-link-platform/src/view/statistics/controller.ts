/*
 * <AUTHOR> 
 * @DateTime 2023-11-14 14:49:05 
 */
import dayjs from 'dayjs';
import StatisticsApi from '@/api/statistics-api';
import * as utils from '@/common/utils';
import { reactive, computed } from 'vue';

import createLoadingModel from '@/model/loading';
import createDialogModel from '@/model/dialog';
import createPageModel from '@/model/page';

export const createProjectController = () => {
    const dialogTaskDataMode = createDialogModel();
    const loadingModelData = createLoadingModel();
    const pageModel = createPageModel();

    const toolsParams = reactive({
        dateRange: [
            dayjs().startOf('month').format('YYYY-MM-DD'),
            dayjs().endOf('month').format('YYYY-MM-DD'),
        ],
        keyword: '',
    });

    const statisticsData = reactive({
        dataList: <object[]>[],
        taskData: null,
    });

    // 匹配的数据列表
    const mateDataList = computed(() => {
        let dataList = statisticsData.dataList;
        if (toolsParams.keyword) {
            // 通过关键字过滤
            const keywordLower = toolsParams.keyword.toLowerCase();
            dataList = dataList.filter((item: any) => (
                item.name?.toLowerCase().includes(keywordLower)
                || item.displayName?.toLowerCase().includes(keywordLower)
            ));
        }
        pageModel.setTotal(dataList.length);
        pageModel.setPage(1);
        return dataList;
    });

    // 展示的数据列表
    const showDataList = computed(() => {
        const { sIndex, eIndex } = pageModel.createSliceParams();
        return mateDataList.value.slice(sIndex, eIndex).map((item: any) => {
            const itemInfo = {
                ...item,
                totalOpenClinicCount: item.totalOpenClinicCount || 0, // 开通门店数
                totalDataCount: item.totalDataCount || 0, // 总任务数
                uploadSuccess: item.uploadSuccess || 0, // 上传成功数
                uploadFailed: item.uploadFailed || 0, // 上传失败数
                uploadPending: utils.red(item.totalDataCount, item.uploadSuccess, item.uploadFailed), // 待上传数
                successRate: 0, // 成功率
                failedRate: 0, // 失败率
                pendingRate: 0, // 待上传率
            };
            Object.assign(itemInfo, {
                successRate: utils.calculatePercentage(itemInfo.uploadSuccess, itemInfo.totalDataCount), // 成功率
                failedRate: utils.calculatePercentage(itemInfo.uploadFailed, itemInfo.totalDataCount), // 失败率
                pendingRate: utils.calculatePercentage(itemInfo.uploadPending, itemInfo.totalDataCount), // 待上传率
            });
            return itemInfo;
        });
    });

    /**
     * 创建统计数据请求参数
     * <AUTHOR>
     * @date 2025-10-30
     * @returns {Object}
     */
    const createParams = () => {
        const params = {
            startTime: toolsParams.dateRange[0], // 开始日期（格式：yyyy-MM-dd）
            endTime: toolsParams.dateRange[1], // 结束日期（格式：yyyy-MM-dd）
        };
        return params;
    };
    
    /**
     * 获取统计数据列表
     * <AUTHOR>
     * @date 2025-09-08
     * @param {Object} params
     * @returns {Promise<AbcResponse>}
     */
    const requestStatisticsData = async (params: any) => {
        const fetchResponse = await StatisticsApi.fetchStatisticsData(params);
        if (fetchResponse.status === false) {
            return fetchResponse;
        }
        return fetchResponse;
    };

    return {
        dialogTaskDataMode,
        loadingModelData,
        pageModel,
        toolsParams,
        statisticsData,
        showDataList,
        createParams,
        requestStatisticsData,
    };
};