.abc-editor__bulletList {
    list-style-type: disc;
    padding: 0 12px;

    .abc-editor__bulletList {
        list-style-type: circle;
    }
}

.abc-editor__list-item {
    line-height: 20px;
    margin-left: 8px;
    display: list-item;
    text-align: -webkit-match-parent;
}

.abc-editor__ordered-list {
    padding: 0 12px;
    list-style-type: decimal;
}

.abc-editor__divider {
    border: none;
    border-bottom: 1px dashed #e6eaee;
    margin: 4px 0;
}

.abc-editor__text-style {
    line-height: 1.5;
    display: inline;
}

h1.abc-editor__heading {
    font-size: 24px;
    margin: 36px 0;
}

h2.abc-editor__heading {
    margin: 28px 0;
}

h3.abc-editor__heading {
    margin: 14px 0;
}

h4.abc-editor__heading {
    margin: 10px 0;
}

h5.abc-editor__heading,
h6.abc-editor__heading {
    margin: 6px 0;
}

.abc-editor__paragraph {
    margin: 6px;
    min-height: 20px;
    line-height: 1;
}

.abc-editor__blockquote {
    border-left: 2px solid rgba(13, 13, 13, .1);
    padding-left: 14px;
}

.abc-editor__code {
    padding: .25em;
    border-radius: .25em;
    background-color: rgba(#616161, .1);
    color: #616161;
    box-decoration-break: clone;
}

.abc-editor__link:-webkit-any-link {
    color: -webkit-link;
    cursor: pointer;
    text-decoration: underline;
}

.abc-editor__code-block {
    background: #0d0d0d;
    border-radius: 8px;
    color: #fff;
    font-family: JetBrainsMono, monospace;
    padding: 12px 16px;
    line-height: 16px;

    code {
        display: inline-block;
        width: 100%;
        overflow-x: auto;
        background: none;
        color: inherit;
        font-size: 16px;
        line-height: 16px;
        padding: 0;
    }
}

.abc-editor__custom-image-wrapper {
    background-color: #f5f5f5;
    border-radius: 4px;
    display: flex;
    flex-wrap: wrap;
    margin: 12px 0;

    img.ProseMirror-separator {
        display: none !important;
    }

    &.flex-around {
        justify-content: space-around !important;
    }

    &.flex-between {
        justify-content: space-between !important;
    }

    &.flex-center {
        justify-content: center !important;
    }
}

@media screen and (min-width: 480px) and (max-width: 767px) {
    .abc-editor__custom-image {
        width: 85% !important;
    }
}

@media screen and (min-width: 320px) and (max-width: 479px) {
    .abc-editor__custom-image {
        width: 100% !important;
    }
}
