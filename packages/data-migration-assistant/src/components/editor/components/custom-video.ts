import { mergeAttributes, Node, nodeInputRule } from '@tiptap/core';
import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state';
import { uploadFile } from '@/components/editor/components/upload-files';

declare module '@tiptap/core' {
    interface Commands<ReturnType> {
        video: {
            /**
             * Add an video
             */
            setVideo: (options: { src: string, controls: Boolean }) => ReturnType,
        }
    }
}
export interface VideoOptions {
    inline: boolean,
    HTMLAttributes: Record<string, any>,
    videoWrapperTypeName: string,
    moduleName: string,
    rootDir: string,
}

export const inputRegex = /(?:^|\s)(!\[(.+|:?)]\((\S+)(?:(?:\s+)["'](\S+)["'])?\))$/;

export const CustomVideoExtension = Node.create<VideoOptions>({
    name: 'video',

    addOptions() {
        return {
            inline: false,
            HTMLAttributes: {},
            videoWrapperTypeName: 'videoWrapper',
            moduleName: '',
            rootDir: '',
        };
    },

    inline: true,

    group: 'inline',

    draggable: true,
    defining: true,

    addAttributes() {
        return {
            src: {
                default: null,
            },
            controls: {
                default: true,
            },
            width: {
                default: null,
            },
            class: {
                default: null,
            },
        };
    },

    parseHTML(): any {
        return [
            {
                tag: 'video',
                getAttrs: (dom: HTMLElement) => ({
                    width: dom.getAttribute('width'),
                    src: dom.getAttribute('src'),
                    controls: dom.getAttribute('controls'),
                    class: dom.className,
                }),
            },
        ];
    },

    renderHTML({ HTMLAttributes }): any {
        const videoAttributes = mergeAttributes(
            HTMLAttributes,
            {
                src: HTMLAttributes.src,
                controls: HTMLAttributes.controls,
                class: 'abc-editor__custom-video' + (HTMLAttributes.class ? ` ${HTMLAttributes.class}` : ''),
                style: `max-width: 100%;height: 300px;${HTMLAttributes.width ? `width: ${HTMLAttributes.width}` : 'width: 100%;'}`,
            },
        );

        return ['video', videoAttributes];
    },

    addCommands() {
        return {
            setVideo: options => ({ commands }) => commands.insertContent({
                type: this.name,
                attrs: options,
            }),
        };
    },

    addInputRules() {
        return [
            nodeInputRule({
                find: inputRegex,
                type: this.type,
                getAttributes: match => {
                    const [,, controls, src] = match;

                    return { src, controls };
                },
            }),
        ];
    },

    addProseMirrorPlugins() {
        const { editor } = this;
        return [
            new Plugin({
                key: new PluginKey('customVideoPastePlugin'),
                props: {
                    handleClickOn: (view: any, pos: number, node: any, nodePos: number, event: MouseEvent) => {
                        console.log(node.type, 'node.type');
                        if (node.type.name === 'video') {
                            const { src } = node.attrs;
                            const { tr } = view.state;
                            const parentNode = view.state.doc.resolve(nodePos).parent;
                            if (parentNode.type.name !== 'videoWrapper') {
                                const wrapperNode = editor.view.state.schema.nodes.videoWrapper.create(
                                    {},
                                    [node],
                                );
                                view.dispatch(tr.replaceRangeWith(nodePos, nodePos + node.nodeSize, wrapperNode));
                                return true;
                            }
                            return false;
                        }
                        return false;
                    },
                    handlePaste: (view: any, event: any) => {
                        // 获取paste的video
                        const items = (event.clipboardData || event.originalEvent.clipboardData).items;
                        let blob = null;
                        let pasteVideoHandled = true;
                        for (let i = 0; i < items.length; i++) {
                            blob = items[i].getAsFile();
                            if (items[i].type.indexOf('video') === 0 && blob !== null) {
                                (async (blob) => {
                                    const uploadRes: any = await uploadFile(blob, this.options.moduleName, this.options.rootDir);
                                    console.log('uploadRes', uploadRes);
                                    if (uploadRes.status === 'done') {
                                        const newNode = editor.view.state.schema.nodes.video.create({
                                            src: uploadRes.url,
                                            controls: true,
                                        });
                                        const { tr } = editor.view.state;
                                        if (tr.selection.$from.node().type.name === 'videoWrapper') {
                                            editor.view.dispatch(tr.replaceSelectionWith(newNode));
                                        } else {
                                            const wrapperNode = editor.view.state.schema.nodes.videoWrapper.create(
                                                {},
                                                [newNode],
                                            );
                                            editor.view.dispatch(tr.replaceSelectionWith(wrapperNode));
                                        }
                                    }
                                })(blob);
                                pasteVideoHandled = false;
                            }
                        }
                        return !pasteVideoHandled; // 只有在未处理粘贴的视频时返回 true，阻止默认规则
                    },
                },
            }),
        ];
    },
});
