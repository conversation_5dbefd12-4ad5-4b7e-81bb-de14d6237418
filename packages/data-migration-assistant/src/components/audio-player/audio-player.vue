<template>
    <div class="audio-player">
        <el-icon v-if="!isPlaying" class="control-icon play-icon" @click="play">
            <VideoPlay />
        </el-icon>
        <el-icon v-else class="control-icon pause-icon" @click="pause">
            <VideoPause />
        </el-icon>
        <div class="progress">
            <input
                v-model="currentTime"
                type="range"
                min="0"
                :max="duration"
                class="progress-bar"
                @input="seek"
            />
            <span class="time">{{ formatAudioTime(currentTime) }} / {{ formatAudioTime(duration) }}</span>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, defineProps } from 'vue';
import { ElIcon } from 'element-plus';
import { globalAudioState } from './state.ts';

const props = defineProps<{ src: string }>();

const audio = new Audio(props.src);
const currentTime = ref(0);
const duration = ref(0);
const isPlaying = ref(false);

const play = () => {
    if (globalAudioState.currentAudio.value && globalAudioState.currentAudio.value !== audio) {
        globalAudioState.currentAudio.value.pause();
    }
    audio.play();
    isPlaying.value = true;
    globalAudioState.currentAudio.value = audio;
};

const pause = () => {
    audio.pause();
    isPlaying.value = false;
    if (globalAudioState.currentAudio.value === audio) {
        globalAudioState.currentAudio.value = null;
    }
};

const seek = (event: Event) => {
    const target = event.target as HTMLInputElement;
    audio.currentTime = Number(target.value);
};

const updateProgress = () => {
    currentTime.value = audio.currentTime;
};

onMounted(() => {
    audio.addEventListener('timeupdate', updateProgress);
    audio.addEventListener('loadedmetadata', () => {
        duration.value = audio.duration;
    });
});

onUnmounted(() => {
    audio.removeEventListener('timeupdate', updateProgress);
    audio.pause();
    if (globalAudioState.currentAudio.value === audio) {
        globalAudioState.currentAudio.value = null;
    }
});

watch(() => props.src, (newSrc) => {
    audio.src = newSrc;
    audio.load();
    isPlaying.value = false;
});

watch(globalAudioState.currentAudio, (newAudio) => {
    if (newAudio !== audio && isPlaying.value) {
        isPlaying.value = false;
    }
});

// Format time filter
const formatAudioTime = (value: number) => {
    const minutes = Math.floor(value / 60);
    const seconds = Math.floor(value % 60).toString().padStart(2, '0');
    return `${minutes}:${seconds}`;
};
</script>

<style scoped>
.audio-player {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 8px;
    padding: 4px; /* 更紧凑的内边距 */
    width: 100%;
    max-width: 350px;
    border: 1px solid rgba(225, 225, 225, .36);
    gap: 6px; /* 更小的间距 */
    height: 36px; /* 更小的面板高度 */
}

.control-icon {
    font-size: 20px;
    cursor: pointer;
    transition: color .3s, transform .3s ease;
    color: #459eff;
}

.control-icon:hover {
    color: #1e3a8a;
}

.play-icon {
    color: #459eff;
}

.pause-icon {
    color: #4b8f99;
}

.progress {
    display: flex;
    align-items: center;
    gap: 6px; /* 更小的间距 */
    flex-grow: 1;
    justify-content: space-between;
    width: 100%;
}

.time {
    font-size: 10px;
    color: var(--oa-text-color-2); /* 灰色字体 */
    white-space: nowrap;
}

.progress-bar {
    flex-grow: 1;
    -webkit-appearance: none;
    height: 4px; /* 缩小进度条高度 */
    background: #e1e1e1;
    border-radius: 4px;
    outline: none;
    transition: background .3s ease;
    width: calc(100% - 32px); /* 适应更紧凑的尺寸 */
}

.progress-bar:hover {
    background: #b2bec3;
}

.progress-bar::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 12px; /* 缩小滑块 */
    height: 12px;
    background: #459eff;
    border-radius: 50%;
    cursor: pointer;
    transition: background .3s ease;
}

.progress-bar::-webkit-slider-thumb:hover {
    background: #007bff;
}

.progress-bar::-moz-range-thumb {
    width: 12px;
    height: 12px;
    background: #459eff;
    border-radius: 50%;
    cursor: pointer;
    transition: background .3s ease;
}

.progress-bar::-moz-range-thumb:hover {
    background: #007bff;
}
</style>
