import { useFormat } from '@/composables/date';
import OSSUtil from '@/utils/oss';

export const uploadFile = async (file: any, moduleName: string, rootDir: string) => {
    const fileName = `${moduleName}_${useFormat(new Date(), 'YYYY-MM-DD HH:mm:ss')}_${file.name}`;
    try {
        const { url } = await OSSUtil.upload({
            bucket: import.meta.env.VITE_APP_OSS_BUCKET,
            region: import.meta.env.VITE_APP_OSS_REGION,
            rootDir,
            fileName,
        }, file);
        file.status = 'done';
        file.message = '';
        file.url = url;
    } catch (e) {
        console.error('e', e);
        file.status = 'failed';
        file.message = '上传失败';
        return null;
    }
    return file;
};