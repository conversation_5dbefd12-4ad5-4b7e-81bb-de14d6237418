// Iconify 插件包装器 - 解决 ESM 兼容性问题
// 使用动态导入方式加载 Iconify 插件

let iconifyPlugin = null;

try {
    // 在这里使用动态导入 Iconify
    const Iconify = require('vite-plugin-iconify');
    iconifyPlugin = Iconify.default || Iconify;
} catch (error) {
    console.warn('无法加载 Iconify 插件:', error);
}

module.exports = function createIconifyPlugin() {
    if (iconifyPlugin) {
        return iconifyPlugin();
    }
    // 返回一个空插件，以便在 Iconify 不可用时不会导致构建失败
    return {
        name: 'iconify-fallback',
        // 实现一个空插件
    };
};
