<script setup lang="ts">
import { useTable } from './controller.ts';
import { onMounted, onUnmounted, PropType, watch } from 'vue';

const props = defineProps({
    columns: {
        type: Array,
        required: true,
    },
    getData: {
        type: Function as PropType<() => Promise<any>>,
        required: true,
    },
    showPagination: {
        type: Boolean,
        default: true,
    },
    immediateSearch: {
        type: Boolean,
        default: true,
    },
});

onMounted(() => {
    if (props.immediateSearch) {
        fetchData();
    }
});
const { tableData, loading, pagination, fetchData, updateColumns, destroy } = useTable(props.getData);
onUnmounted(() => {
    destroy();
});
updateColumns(props.columns);

watch(() => props.columns, (newColumns) => {
    updateColumns(newColumns);
});

const handleSizeChange = (val: number) => {
    pagination.value.pageSize = val;
    fetchData();
};

const handleCurrentChange = (val: number) => {
    pagination.value.page = val;
    fetchData();
};

defineExpose({
    fetchData,
});
</script>
<template>
    <el-table v-loading="loading" class="oa-table-wrapper" :data="tableData">
        <el-table-column
            v-for="column in columns"
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            :align="column.align"
        >
            <template #default="scope">
                <slot :name="column.slot" :row="scope.row" :column="column">{{ scope.row[column.prop] }}</slot>
            </template>
        </el-table-column>
    </el-table>
    <el-pagination
        v-if="showPagination"
        class="oa-table-pagination"
        :current-page="pagination.page"
        :page-sizes="[10, 20, 50]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
    >
    </el-pagination>
</template>
<style lang="scss" scoped>
.oa-table-pagination {
    margin-top: 20px;
    justify-content: flex-end;
}
</style>
