<script setup lang="ts">
import { PropType } from 'vue';
import { RouteRecordRaw } from 'vue-router';

const props = defineProps({
    menus: {
        type: Array as PropType<RouteRecordRaw[]>,
        default: () => [],
    },
});

const emit = defineEmits(['click']);

function handleMenuClick(menu: any) {
    emit('click', menu);
}
</script>
<template>
    <div class="grid-menu-group-wrapper">
        <div v-for="menu in menus" :key="menu.path" class="menu-item-wrapper">
            <div class="menu-item" @click="handleMenuClick(menu)">
                <el-icon
                    v-if="menu.meta?.icon"
                    class="menu_item__icon"
                    size="16px"
                >
                    <component :is="menu.meta.icon"></component>
                </el-icon>
                <span class="menu-item__name">{{ menu.meta?.name }}</span>
            </div>
        </div>
    </div>
</template>

<style lang="scss">
    .grid-menu-group-wrapper {
        display: flex;
        flex-wrap: wrap;
        background-color: white;
        border-radius: var(--oa-border-radius-4);
        overflow: hidden;

        .menu-item-wrapper {
            width: 33.33%;
            border-bottom: 1px solid var(--oa-border-color);
            cursor: pointer;

            &:nth-child(3n+1) {
                border-right: 1px solid var(--oa-border-color);
            }

            &:nth-child(3n+2) {
                border-right: 1px solid var(--oa-border-color);
            }

            .menu-item {
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                height: 86px;

                .el-icon {
                    font-size: 18px;
                    margin-bottom: 8px;
                }
            }
        }
    }
</style>
