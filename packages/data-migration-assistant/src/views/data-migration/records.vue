<script setup lang="ts">
import { defineAsyncComponent, onUnmounted, ref, watch } from 'vue';

import LayoutPageContainer from '@/layout/components/page-container/layout-page-container.vue';
import { useLayoutQuickListHook } from './hook/quick-list.ts';
import { useRouter, useRoute } from 'vue-router';
import { DataMigrationRouteKey } from '@/views/data-migration/route.ts';
import { ElMessage } from 'element-plus';

const LayoutQuickListSidebar = defineAsyncComponent(() => import('@/layout/components/quick-list-wrapper/layout-quick-list-sidebar.vue'));
const AddClinicDialog = defineAsyncComponent(() => import('@/views/data-migration/components/add-clinic-dialog/index.vue'));

const router = useRouter();
const route = useRoute();

const showAddClinicDialog = ref(false);

const {
    quickListShow,
    quickListTotal,
    updateKeyword,
    setQuickListSelectedItem,
    createUniqueKey,
    parseUnique<PERSON>ey,
    fetchDataTransferClinicList,
    destroy,
} = useLayoutQuickListHook();
let isInit = true;

onUnmounted(() => {
    destroy();
});

watch(() => route.params.id, async (id) => {
    // 判断是否是当前路由
    if (route.matched?.findIndex((item) => item.name === DataMigrationRouteKey.Index) === -1) {
        return;
    }
    if (isInit) {
        await fetchDataTransferClinicList();
        isInit = false;
    }
    if (id) {
        setQuickListSelectedItem(parseUniqueKey(id));
    } else {
        const item = quickListShow.value[0];
        if (!item) {
            return;
        }
        // 将 id 设置到 路由的 :id 参数中
        await router.replace({
            name: DataMigrationRouteKey.Main,
            params: {
                id: createUniqueKey({
                    clinicId: item.clinicId,
                    chainId: item.chainId,
                }),
            },
        });
        setQuickListSelectedItem({
            clinicId: item.clinicId,
            chainId: item.chainId,
        });
    }
}, {
    immediate: true,
});

async function handleQuickListItemClick(item: any) {
    await router.push({
        name: DataMigrationRouteKey.Main,
        params: {
            id: createUniqueKey({
                clinicId: item.clinicId,
                chainId: item.chainId,
            }),
        },
    });
}

const handleCreate = () => {
    showAddClinicDialog.value = true;
};

const addClinicCallBack = (item: any) => {
    fetchDataTransferClinicList();
    handleQuickListItemClick(item);
};

</script>
<template>
    <layout-page-container>
        <template #sidebar>
            <layout-quick-list-sidebar
                :need-create="true"
                :quick-list="quickListShow"
                :quick-list-total="quickListTotal"
                search-placeholder="门店名/成员手机号"
                @search="updateKeyword"
                @create="handleCreate"
                @item-click="handleQuickListItemClick"
            >
                <template #toolbar>
                    <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                        <span>共 {{ quickListTotal }}</span>
                    </div>
                </template>
                <template #search-extra>
                </template>
                <template #list-item="{item}">
                    <div class="follow-up-list-item">
                        <div class="line1">
                            <oa-icon icon="fa-solid:clinic-medical" :color="item.isActive ? 'white':'#287ef1'"></oa-icon>
                            <span class="follow-up-list-item-clinic-name">{{ item.clinicName }}</span>
                        </div>
                    </div>
                </template>
            </layout-quick-list-sidebar>
        </template>
        <router-view></router-view>
        <AddClinicDialog v-if="showAddClinicDialog" v-model="showAddClinicDialog" @submit="addClinicCallBack"></AddClinicDialog>
    </layout-page-container>
</template>

<style lang="scss">
@use "@/style/mixins/mixins.scss" as mixins;

.follow-up-list-item {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 4px;

    .line1 {
        display: flex;
        align-items: center;
        gap: 4px;

        .follow-up-list-item-remark-sign {
            max-width: 24px;
            margin-right: 8px;
        }

        .follow-up-list-item-clinic-name {
            flex: 1;
        }
    }
}
</style>
