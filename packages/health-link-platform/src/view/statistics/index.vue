<template>
    <div class="view-statistics">
        <div class="tools-warpper">
            <el-date-picker
                v-model="toolsParams.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 200px;"
                :shortcuts="shortcuts"
                :popper-options="{
                    placement: 'bottom-start',
                }"
                @change="fetchStatisticsData"
            />
            <el-input
                v-model="toolsParams.keyword"
                class="search-input"
                :suffix-icon="Search"
                clearable
                placeholder="Key / 项目名称"
            />
            <div class="track-box"></div>
        </div>
        <div class="content-wrapper">
            <el-table
                v-loading="loadingModelData.loading.value"
                :data="showDataList"
                :height="530"
                style="width: 100%;"
            >
                <el-table-column
                    prop="name"
                    label="项目Key"
                    min-width="200"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="displayName"
                    label="项目名称"
                    min-width="260"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="totalOpenClinicCount"
                    label="开通门店数"
                    min-width="160"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="totalDataCount"
                    label="任务总数"
                    min-width="180"
                    show-overflow-tooltip
                />
                <el-table-column
                    prop="uploadSuccess"
                    label="上报成功"
                    min-width="180"
                    show-overflow-tooltip
                >
                    <template #default="scope">
                        <el-text type="success">
                            {{ scope.row.uploadSuccess }} / {{ scope.row.successRate }}%
                        </el-text>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="uploadFailed"
                    label="上报失败"
                    min-width="180"
                    show-overflow-tooltip
                >
                    <template #default="scope">
                        <el-text type="warning">
                            {{ scope.row.uploadFailed }} / {{ scope.row.failedRate }}%
                        </el-text>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="uploadPending"
                    label="待上报"
                    min-width="180"
                    show-overflow-tooltip
                >
                    <template #default="scope">
                        <el-text type="primary">
                            {{ scope.row.uploadPending }} / {{ scope.row.pendingRate }}%
                        </el-text>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="remark"
                    label="操作"
                    width="120"
                    fixed="right"
                >
                    <template #default="scope">
                        <div class="align-center">
                            <el-button
                                plain
                                type="primary"
                                @click="onClickTaskData(scope.row)"
                            >
                                查看任务
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <div class="footer-box">
                <el-pagination
                    v-model:current-page="pageModel.params.page"
                    :page-size="pageModel.params.pageSize"
                    :total="pageModel.params.total"
                    background
                    layout="total, prev, pager, next"
                />
            </div>
        </div>

        <dialog-task-data
            v-if="dialogTaskDataMode.visible.value"
            v-model="dialogTaskDataMode.visible.value"
            :date-range="toolsParams.dateRange"
            :task-data="statisticsData.taskData"
        />
    </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { Search } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { createProjectController } from './controller';
import * as utils from '@/common/utils';

import DialogTaskData from './dialog-task-data/index.vue';

// 日期快捷选择
const shortcuts = utils.createShortcuts();

const {
    dialogTaskDataMode,
    loadingModelData,
    pageModel,
    statisticsData,
    toolsParams,
    showDataList,
    createParams,
    requestStatisticsData,
} = createProjectController();

onMounted(() => {
    fetchStatisticsData();
});

/**
 * 获取统计数据列表
 * <AUTHOR>
 * @date 2025-09-09
 */
const fetchStatisticsData = async () => {
    loadingModelData.setLoading(true);
    const params = createParams();
    const fetchResponse = await requestStatisticsData(params);
    if (!utils.isEqual(params, createParams())) {
        return;
    }
    loadingModelData.setLoading(false);
    if (fetchResponse.status === false) {
        ElMessage.error(fetchResponse.message);
        return;
    }
    statisticsData.dataList = fetchResponse.data?.projects || [];
};

/**
 * 查看任务数据
 * <AUTHOR>
 * @date 2025-09-03
 */
const onClickTaskData = (row: any) => {
    statisticsData.taskData = row;
    dialogTaskDataMode.show();
};
</script>

<style lang="scss">
.view-statistics {
    .tools-warpper {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 8px;

        .el-date-editor {
            max-width: 260px;
        }

        .track-box {
            flex: 1;
        }

        .search-input {
            width: 240px;
        }
    }

    .content-wrapper {
        margin-top: 16px;

        .footer-box {
            margin-top: 16px;
            display: flex;
            justify-content: flex-end;
        }
    }
}
</style>
