import Layout from '@/layout/index.vue';

export const dynamicRoutes = [
    {
        path: '/',
        name: 'layout',
        component: Layout,
        meta: {
            name: '首页',
        },
        redirect: {
            name: '@home',
        },
        children: [
            {
                path: 'home',
                name: '@home',
                component: () => import('@/view/home/<USER>'),
                meta: {
                    name: '首页',
                    icon: 'vaadin:home-o',
                },
            },
            {
                path: 'project',
                name: '@project',
                component: () => import('@/view/project/index.vue'),
                meta: {
                    name: '项目管理',
                    icon: 'lucide:folder-closed',
                },
            },
            {
                path: 'project/:projectId',
                name: '@project-task',
                component: () => import('@/view/project-task/index.vue'),
                meta: {
                    name: '任务管理',
                    hide: true,
                    prev: '@project',
                },
            },
            {
                path: 'statistics',
                name: '@statistics',
                component: () => import('@/view/statistics/index.vue'),
                meta: {
                    name: '统计分析',
                    icon: 'lucide:chart-column-stacked',
                },
            },
            {
                path: '/:pathMatch(.*)',
                name: 'notfound',
                component: () => import('@/view/not-found/index.vue'),
                meta: {
                    hide: true,
                },
            },
        ],
    },
];
